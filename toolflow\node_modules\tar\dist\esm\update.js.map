{"version": 3, "file": "update.js", "sourceRoot": "", "sources": ["../../src/update.ts"], "names": [], "mappings": "AAAA,SAAS;AAET,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAA;AAG/C,OAAO,EAAE,OAAO,IAAI,CAAC,EAAE,MAAM,cAAc,CAAA;AAE3C,iDAAiD;AACjD,MAAM,CAAC,MAAM,MAAM,GAAG,WAAW,CAC/B,CAAC,CAAC,QAAQ,EACV,CAAC,CAAC,SAAS,EACX,CAAC,CAAC,UAAU,EACZ,CAAC,CAAC,WAAW,EACb,CAAC,GAAG,EAAE,OAAO,GAAG,EAAE,EAAE,EAAE;IACpB,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;IAC1B,WAAW,CAAC,GAAG,CAAC,CAAA;AAClB,CAAC,CACF,CAAA;AAED,MAAM,WAAW,GAAG,CAAC,GAA0B,EAAE,EAAE;IACjD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;IAEzB,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;QACpB,GAAG,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAA;IAC5B,CAAC;IAED,GAAG,CAAC,MAAM;QACR,MAAM,CAAC,CAAC;YACN,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CACb,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;gBAClB,CAAC;gBACC,qBAAqB;gBACrB,CACE,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;oBAC9C,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAClB;gBACD,oBAAoB;iBACrB;YACL,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CACb,CAAC;YACC,qBAAqB;YACrB,CACE,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;gBAC9C,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAClB;YACD,oBAAoB;aACrB,CAAA;AACT,CAAC,CAAA", "sourcesContent": ["// tar -u\n\nimport { makeCommand } from './make-command.js'\nimport { type TarOptionsWithAliases } from './options.js'\n\nimport { replace as r } from './replace.js'\n\n// just call tar.r with the filter and mtimeCache\nexport const update = makeCommand(\n  r.syncFile,\n  r.asyncFile,\n  r.syncNoFile,\n  r.asyncNoFile,\n  (opt, entries = []) => {\n    r.validate?.(opt, entries)\n    mtimeFilter(opt)\n  },\n)\n\nconst mtimeFilter = (opt: TarOptionsWithAliases) => {\n  const filter = opt.filter\n\n  if (!opt.mtimeCache) {\n    opt.mtimeCache = new Map()\n  }\n\n  opt.filter =\n    filter ?\n      (path, stat) =>\n        filter(path, stat) &&\n        !(\n          /* c8 ignore start */\n          (\n            (opt.mtimeCache?.get(path) ?? stat.mtime ?? 0) >\n            (stat.mtime ?? 0)\n          )\n          /* c8 ignore stop */\n        )\n    : (path, stat) =>\n        !(\n          /* c8 ignore start */\n          (\n            (opt.mtimeCache?.get(path) ?? stat.mtime ?? 0) >\n            (stat.mtime ?? 0)\n          )\n          /* c8 ignore stop */\n        )\n}\n"]}