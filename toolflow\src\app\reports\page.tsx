'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { 
  FileText,
  Download,
  Calendar,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  AlertTriangle,
  CheckCircle,
  Clock,
  Package,
  Wrench
} from 'lucide-react';

const reportSummary = {
  totalBorrowings: 156,
  totalReturns: 142,
  activeBorrowings: 14,
  overdueBorrowings: 3,
  totalConsumption: 89,
  damagedReturns: 7,
  monthlyTrend: 12.5
};

const topBorrowers = [
  { name: "<PERSON><PERSON>", count: 23, status: "active" },
  { name: "<PERSON><PERSON>", count: 19, status: "good" },
  { name: "<PERSON>", count: 17, status: "overdue" },
  { name: "<PERSON><PERSON>", count: 15, status: "active" },
  { name: "<PERSON><PERSON>", count: 12, status: "good" }
];

const topItems = [
  { name: "<PERSON><PERSON><PERSON>", borrowed: 45, category: "Tools" },
  { name: "Meteran Digital", borrowed: 38, category: "Tools" },
  { name: "Set Bor Listrik", borrowed: 32, category: "Tools" },
  { name: "Besi Beton 12mm", consumed: 250, category: "Materials", unit: "kg" },
  { name: "Semen Portland", consumed: 180, category: "Materials", unit: "sak" }
];

const monthlyData = [
  { month: "Jan", borrowings: 45, returns: 42, consumption: 28 },
  { month: "Feb", borrowings: 52, returns: 48, consumption: 35 },
  { month: "Mar", borrowings: 38, returns: 41, consumption: 22 },
  { month: "Apr", borrowings: 61, returns: 58, consumption: 41 },
  { month: "May", borrowings: 55, returns: 52, consumption: 38 },
  { month: "Jun", borrowings: 67, returns: 63, consumption: 45 }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'text-blue-600';
    case 'good':
      return 'text-green-600';
    case 'overdue':
      return 'text-red-600';
    default:
      return 'text-gray-600';
  }
};

export default function Reports() {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Reports</h1>
          <p className="text-muted-foreground mt-1">
            Comprehensive analytics and reporting dashboard
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" className="hover-lift">
            <Calendar className="w-4 h-4 mr-2" />
            Date Range
          </Button>
          <Button className="hover-lift">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="glass rounded-xl p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Borrowings</p>
              <p className="text-2xl font-bold text-foreground">{reportSummary.totalBorrowings}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-2 flex items-center">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-sm text-green-600">+{reportSummary.monthlyTrend}%</span>
            <span className="text-sm text-muted-foreground ml-1">vs last month</span>
          </div>
        </div>

        <div className="glass rounded-xl p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Returns</p>
              <p className="text-2xl font-bold text-foreground">{reportSummary.totalReturns}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
          <div className="mt-2">
            <span className="text-sm text-muted-foreground">
              {reportSummary.activeBorrowings} still active
            </span>
          </div>
        </div>

        <div className="glass rounded-xl p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Consumption</p>
              <p className="text-2xl font-bold text-foreground">{reportSummary.totalConsumption}</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <TrendingDown className="w-6 h-6 text-orange-600" />
            </div>
          </div>
          <div className="mt-2">
            <span className="text-sm text-muted-foreground">Materials used</span>
          </div>
        </div>

        <div className="glass rounded-xl p-6 hover-lift">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Issues</p>
              <p className="text-2xl font-bold text-foreground">
                {reportSummary.overdueBorrowings + reportSummary.damagedReturns}
              </p>
            </div>
            <div className="p-3 bg-red-100 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
          </div>
          <div className="mt-2">
            <span className="text-sm text-red-600">
              {reportSummary.overdueBorrowings} overdue, {reportSummary.damagedReturns} damaged
            </span>
          </div>
        </div>
      </div>

      {/* Report Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="glass">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="borrowers">Top Borrowers</TabsTrigger>
          <TabsTrigger value="items">Popular Items</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="glass rounded-xl p-6">
            <h3 className="text-lg font-semibold mb-4">Monthly Activity Overview</h3>
            <div className="h-64 flex items-center justify-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
              <div className="text-center">
                <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Chart visualization will be implemented here</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Monthly borrowings, returns, and consumption trends
                </p>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="borrowers" className="space-y-4">
          <div className="glass rounded-xl p-6">
            <h3 className="text-lg font-semibold mb-4">Top Borrowers</h3>
            <div className="space-y-3">
              {topBorrowers.map((borrower, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-primary">{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium text-foreground">{borrower.name}</p>
                      <p className="text-sm text-muted-foreground">{borrower.count} borrowings</p>
                    </div>
                  </div>
                  <Badge className={cn("capitalize", getStatusColor(borrower.status))}>
                    {borrower.status}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="items" className="space-y-4">
          <div className="glass rounded-xl p-6">
            <h3 className="text-lg font-semibold mb-4">Most Popular Items</h3>
            <div className="space-y-3">
              {topItems.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      {item.category === 'Tools' ? 
                        <Wrench className="w-5 h-5 text-primary" /> : 
                        <Package className="w-5 h-5 text-primary" />
                      }
                    </div>
                    <div>
                      <p className="font-medium text-foreground">{item.name}</p>
                      <p className="text-sm text-muted-foreground">{item.category}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-foreground">
                      {item.borrowed || item.consumed} {item.unit || 'times'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {item.borrowed ? 'borrowed' : 'consumed'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <div className="glass rounded-xl p-6">
            <h3 className="text-lg font-semibold mb-4">Activity Trends</h3>
            <div className="h-64 flex items-center justify-center bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
              <div className="text-center">
                <PieChart className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Trend analysis will be implemented here</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Usage patterns, peak times, and seasonal trends
                </p>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
