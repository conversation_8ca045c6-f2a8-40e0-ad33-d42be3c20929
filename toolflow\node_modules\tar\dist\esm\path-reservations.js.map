{"version": 3, "file": "path-reservations.js", "sourceRoot": "", "sources": ["../../src/path-reservations.ts"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,iCAAiC;AACjC,qDAAqD;AACrD,mDAAmD;AACnD,EAAE;AACF,yDAAyD;AACzD,qDAAqD;AAErD,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAA;AAChC,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAA;AACzD,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAA;AAElE,MAAM,QAAQ,GACZ,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,OAAO,CAAC,QAAQ,CAAA;AAC3D,MAAM,SAAS,GAAG,QAAQ,KAAK,OAAO,CAAA;AAStC,+CAA+C;AAC/C,0DAA0D;AAC1D,MAAM,OAAO,GAAG,CAAC,IAAY,EAAE,EAAE;IAC/B,MAAM,IAAI,GAAG,IAAI;SACd,KAAK,CAAC,GAAG,CAAC;SACV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACZ,MAAM,CAAC,CAAC,GAAa,EAAE,IAAI,EAAE,EAAE;QAC9B,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAC7B,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;YACpB,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;QACtB,CAAC;QACD,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,CAAA;QACrB,OAAO,GAAG,CAAA;IACZ,CAAC,EAAE,EAAE,CAAC,CAAA;IACR,OAAO,IAAI,CAAA;AACb,CAAC,CAAA;AAED,MAAM,OAAO,gBAAgB;IAC3B,4BAA4B;IAC5B,6CAA6C;IAC7C,4CAA4C;IAC5C,OAAO,GAAG,IAAI,GAAG,EAAsC,CAAA;IAEvD,6CAA6C;IAC7C,aAAa,GAAG,IAAI,GAAG,EAAwB,CAAA;IAE/C,8BAA8B;IAC9B,QAAQ,GAAG,IAAI,GAAG,EAAW,CAAA;IAE7B,OAAO,CAAC,KAAe,EAAE,EAAW;QAClC,KAAK;YACH,SAAS,CAAC,CAAC;gBACT,CAAC,gCAAgC,CAAC;gBACpC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBACZ,iEAAiE;oBACjE,OAAO,oBAAoB,CACzB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAC1B,CAAC,WAAW,EAAE,CAAA;gBACjB,CAAC,CAAC,CAAA;QAEN,MAAM,IAAI,GAAG,IAAI,GAAG,CAClB,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAC/D,CAAA;QACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAA;QAC3C,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC;YACtB,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;YAC7B,IAAI,CAAC,CAAC,EAAE,CAAC;gBACP,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;YAC3B,CAAC;iBAAM,CAAC;gBACN,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACZ,CAAC;QACH,CAAC;QACD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC/B,IAAI,CAAC,CAAC,EAAE,CAAC;gBACP,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;YACxC,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;gBACzB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;oBACrB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;gBACX,CAAC;qBAAM,CAAC;oBACN,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;gBACvB,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IACtB,CAAC;IAED,2DAA2D;IAC3D,sBAAsB;IACtB,UAAU,CAAC,EAAW;QAIpB,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QACtC,qBAAqB;QACrB,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA;QACjE,CAAC;QACD,oBAAoB;QACpB,OAAO;YACL,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE,CACpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CACR;YAChB,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAGjD;SACN,CAAA;IACH,CAAC;IAED,yDAAyD;IACzD,mDAAmD;IACnD,KAAK,CAAC,EAAW;QACf,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;QAC3C,OAAO,CACL,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAC1D,CAAA;IACH,CAAC;IAED,iEAAiE;IACjE,IAAI,CAAC,EAAW;QACd,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAA;QACd,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QACrB,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QACzB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,CAAC,EAAW;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YAC3B,OAAO,KAAK,CAAA;QACd,CAAC;QACD,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QACtC,qBAAqB;QACrB,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAA;QACxC,CAAC;QACD,oBAAoB;QACpB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,GAAG,CAAA;QAE3B,MAAM,IAAI,GAAG,IAAI,GAAG,EAAW,CAAA;QAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAChC,qBAAqB;YACrB,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;gBACxB,SAAQ;YACV,CAAC;YACD,oBAAoB;YACpB,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACf,IAAI,CAAC,EAAE,EAAE,CAAC;gBACR,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;gBACzB,SAAQ;YACV,CAAC;YACD,CAAC,CAAC,KAAK,EAAE,CAAA;YACT,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE,CAAC;gBAC7B,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;YACd,CAAC;iBAAM,CAAC;gBACN,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;oBACnB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBACb,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC/B,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;YACjB,uCAAuC;YACvC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,YAAY,GAAG,CAAC;gBAAE,SAAQ;YACxC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;gBACxB,SAAQ;YACV,CAAC;iBAAM,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBACzB,CAAC,CAAC,KAAK,EAAE,CAAA;gBACT,+BAA+B;gBAC/B,uCAAuC;gBACvC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;gBACd,IAAI,OAAO,CAAC,KAAK,UAAU,EAAE,CAAC;oBAC5B,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBACb,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;YACf,CAAC;QACH,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;QACxB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;QACjC,OAAO,IAAI,CAAA;IACb,CAAC;CACF", "sourcesContent": ["// A path exclusive reservation system\n// reserve([list, of, paths], fn)\n// When the fn is first in line for all its paths, it\n// is called with a cb that clears the reservation.\n//\n// Used by async unpack to avoid clobbering paths in use,\n// while still allowing maximal safe parallelization.\n\nimport { join } from 'node:path'\nimport { normalizeUnicode } from './normalize-unicode.js'\nimport { stripTrailingSlashes } from './strip-trailing-slashes.js'\n\nconst platform =\n  process.env.TESTING_TAR_FAKE_PLATFORM || process.platform\nconst isWindows = platform === 'win32'\n\nexport type Reservation = {\n  paths: string[]\n  dirs: Set<string>\n}\n\nexport type Handler = (clear: () => void) => void\n\n// return a set of parent dirs for a given path\n// '/a/b/c/d' -> ['/', '/a', '/a/b', '/a/b/c', '/a/b/c/d']\nconst getDirs = (path: string) => {\n  const dirs = path\n    .split('/')\n    .slice(0, -1)\n    .reduce((set: string[], path) => {\n      const s = set[set.length - 1]\n      if (s !== undefined) {\n        path = join(s, path)\n      }\n      set.push(path || '/')\n      return set\n    }, [])\n  return dirs\n}\n\nexport class PathReservations {\n  // path => [function or Set]\n  // A Set object means a directory reservation\n  // A fn is a direct reservation on that path\n  #queues = new Map<string, (Handler | Set<Handler>)[]>()\n\n  // fn => {paths:[path,...], dirs:[path, ...]}\n  #reservations = new Map<Handler, Reservation>()\n\n  // functions currently running\n  #running = new Set<Handler>()\n\n  reserve(paths: string[], fn: Handler) {\n    paths =\n      isWindows ?\n        ['win32 parallelization disabled']\n      : paths.map(p => {\n          // don't need normPath, because we skip this entirely for windows\n          return stripTrailingSlashes(\n            join(normalizeUnicode(p)),\n          ).toLowerCase()\n        })\n\n    const dirs = new Set<string>(\n      paths.map(path => getDirs(path)).reduce((a, b) => a.concat(b)),\n    )\n    this.#reservations.set(fn, { dirs, paths })\n    for (const p of paths) {\n      const q = this.#queues.get(p)\n      if (!q) {\n        this.#queues.set(p, [fn])\n      } else {\n        q.push(fn)\n      }\n    }\n    for (const dir of dirs) {\n      const q = this.#queues.get(dir)\n      if (!q) {\n        this.#queues.set(dir, [new Set([fn])])\n      } else {\n        const l = q[q.length - 1]\n        if (l instanceof Set) {\n          l.add(fn)\n        } else {\n          q.push(new Set([fn]))\n        }\n      }\n    }\n    return this.#run(fn)\n  }\n\n  // return the queues for each path the function cares about\n  // fn => {paths, dirs}\n  #getQueues(fn: Handler): {\n    paths: Handler[][]\n    dirs: (Handler | Set<Handler>)[][]\n  } {\n    const res = this.#reservations.get(fn)\n    /* c8 ignore start */\n    if (!res) {\n      throw new Error('function does not have any path reservations')\n    }\n    /* c8 ignore stop */\n    return {\n      paths: res.paths.map((path: string) =>\n        this.#queues.get(path),\n      ) as Handler[][],\n      dirs: [...res.dirs].map(path => this.#queues.get(path)) as (\n        | Handler\n        | Set<Handler>\n      )[][],\n    }\n  }\n\n  // check if fn is first in line for all its paths, and is\n  // included in the first set for all its dir queues\n  check(fn: Handler) {\n    const { paths, dirs } = this.#getQueues(fn)\n    return (\n      paths.every(q => q && q[0] === fn) &&\n      dirs.every(q => q && q[0] instanceof Set && q[0].has(fn))\n    )\n  }\n\n  // run the function if it's first in line and not already running\n  #run(fn: Handler) {\n    if (this.#running.has(fn) || !this.check(fn)) {\n      return false\n    }\n    this.#running.add(fn)\n    fn(() => this.#clear(fn))\n    return true\n  }\n\n  #clear(fn: Handler) {\n    if (!this.#running.has(fn)) {\n      return false\n    }\n    const res = this.#reservations.get(fn)\n    /* c8 ignore start */\n    if (!res) {\n      throw new Error('invalid reservation')\n    }\n    /* c8 ignore stop */\n    const { paths, dirs } = res\n\n    const next = new Set<Handler>()\n    for (const path of paths) {\n      const q = this.#queues.get(path)\n      /* c8 ignore start */\n      if (!q || q?.[0] !== fn) {\n        continue\n      }\n      /* c8 ignore stop */\n      const q0 = q[1]\n      if (!q0) {\n        this.#queues.delete(path)\n        continue\n      }\n      q.shift()\n      if (typeof q0 === 'function') {\n        next.add(q0)\n      } else {\n        for (const f of q0) {\n          next.add(f)\n        }\n      }\n    }\n\n    for (const dir of dirs) {\n      const q = this.#queues.get(dir)\n      const q0 = q?.[0]\n      /* c8 ignore next - type safety only */\n      if (!q || !(q0 instanceof Set)) continue\n      if (q0.size === 1 && q.length === 1) {\n        this.#queues.delete(dir)\n        continue\n      } else if (q0.size === 1) {\n        q.shift()\n        // next one must be a function,\n        // or else the Set would've been reused\n        const n = q[0]\n        if (typeof n === 'function') {\n          next.add(n)\n        }\n      } else {\n        q0.delete(fn)\n      }\n    }\n\n    this.#running.delete(fn)\n    next.forEach(fn => this.#run(fn))\n    return true\n  }\n}\n"]}