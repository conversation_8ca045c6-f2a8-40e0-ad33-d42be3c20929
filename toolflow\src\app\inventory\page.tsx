'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { InventorySidebar } from '@/components/ui/inventory-sidebar';
import { cn } from '@/lib/utils';
import {
  Package,
  Wrench,
  Plus,
  Search,
  Filter,
  AlertTriangle,
  CheckCircle,
  Clock,
  Edit,
  Trash2,
  ArrowRightLeft,
  TrendingDown,
  Eye,
  MoreHorizontal
} from 'lucide-react';

const inventoryData = {
  tools: [
    {
      id: 'T001',
      name: '<PERSON><PERSON><PERSON>',
      type: 'tool' as const,
      category: 'hand-tools',
      total: 50,
      available: 35,
      borrowed: 10,
      damaged: 5,
      condition: 'mixed',
      location: 'Gudang A - Rak 1',
      threshold: 10,
      lastUpdated: '2024-01-15'
    },
    {
      id: 'T002',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      type: 'tool' as const,
      category: 'power-tools',
      total: 8,
      available: 6,
      borrowed: 2,
      damaged: 0,
      condition: 'good',
      location: 'Gudang A - Rak 3',
      threshold: 2,
      lastUpdated: '2024-01-14'
    },
    {
      id: 'T003',
      name: 'Meteran',
      type: 'tool' as const,
      category: 'measuring',
      total: 25,
      available: 20,
      borrowed: 5,
      damaged: 0,
      condition: 'good',
      location: 'Gudang B - Rak 2',
      threshold: 5,
      lastUpdated: '2024-01-13'
    },
    {
      id: 'T004',
      name: 'Perlengkapan Keselamatan',
      type: 'tool' as const,
      category: 'safety',
      total: 100,
      available: 85,
      borrowed: 15,
      damaged: 0,
      condition: 'good',
      location: 'Gudang C - Rak 1',
      threshold: 20,
      lastUpdated: '2024-01-12'
    },
    {
      id: 'T005',
      name: 'Set Bor',
      type: 'tool' as const,
      category: 'power-tools',
      total: 15,
      available: 12,
      borrowed: 2,
      damaged: 1,
      condition: 'mixed',
      location: 'Gudang A - Rak 4',
      threshold: 3,
      lastUpdated: '2024-01-11'
    }
  ],
  materials: [
    {
      id: 'M001',
      name: 'Besi Beton',
      type: 'material' as const,
      category: 'construction',
      total: 500,
      available: 125,
      consumed: 375,
      unit: 'kg',
      location: 'Gudang D - Area 1',
      threshold: 100,
      lastUpdated: '2024-01-15',
      isLowStock: true
    },
    {
      id: 'M002',
      name: 'Semen Cor',
      type: 'material' as const,
      category: 'construction',
      total: 1000,
      available: 400,
      consumed: 600,
      unit: 'kg',
      location: 'Gudang D - Area 2',
      threshold: 200,
      lastUpdated: '2024-01-14',
      isLowStock: false
    },
    {
      id: 'M003',
      name: 'Kabel Listrik',
      type: 'material' as const,
      category: 'electrical',
      total: 1000,
      available: 800,
      consumed: 200,
      unit: 'meter',
      location: 'Gudang E - Rak 1',
      threshold: 100,
      lastUpdated: '2024-01-13',
      isLowStock: false
    },
    {
      id: 'M004',
      name: 'Cat Tembok',
      type: 'material' as const,
      category: 'construction',
      total: 200,
      available: 45,
      consumed: 155,
      unit: 'liter',
      location: 'Gudang F - Rak 2',
      threshold: 50,
      lastUpdated: '2024-01-12',
      isLowStock: true
    }
  ]
};

export default function Inventory() {
  const [activeTab, setActiveTab] = useState("tools");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarType, setSidebarType] = useState<'create' | 'edit' | 'process' | 'delete'>('create');
  const [editItem, setEditItem] = useState<any>(null);

  const handleCreateClick = () => {
    setSidebarType('create');
    setSidebarOpen(true);
  };

  const handleEditClick = (item: any) => {
    setEditItem(item);
    setSidebarType('edit');
    setSidebarOpen(true);
  };

  const handleProcessClick = () => {
    if (selectedItems.length === 0) return;
    setSidebarType('process');
    setSidebarOpen(true);
  };

  const handleDeleteClick = () => {
    if (selectedItems.length === 0) return;
    setSidebarType('delete');
    setSidebarOpen(true);
  };

  const handleFormSubmit = (formData: any) => {
    console.log('Form submitted:', formData);
    setSelectedItems([]);
    // Handle form submission logic here
  };

  const toggleItemSelection = (item: any) => {
    setSelectedItems(prev => {
      const isSelected = prev.some(selected => selected.id === item.id);
      if (isSelected) {
        return prev.filter(selected => selected.id !== item.id);
      } else {
        return [...prev, item];
      }
    });
  };

  const getConditionBadge = (condition: string) => {
    switch (condition) {
      case 'good':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Good</Badge>;
      case 'mixed':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Mixed</Badge>;
      case 'poor':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Poor</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Unknown</Badge>;
    }
  };

  const getStatusIcon = (type: string, item: any) => {
    if (type === 'material' && item.isLowStock) {
      return <AlertTriangle className="w-4 h-4 text-red-500" />;
    }
    return type === 'tool' ? <Wrench className="w-4 h-4 text-primary" /> : <Package className="w-4 h-4 text-primary" />;
  };

  const currentData = activeTab === 'tools' ? inventoryData.tools : inventoryData.materials;
  const filteredData = currentData.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Inventory</h1>
          <p className="text-muted-foreground mt-1">
            Manage tools and materials inventory
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" className="hover-lift">
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </Button>
          <Button onClick={handleCreateClick} className="hover-lift">
            <Plus className="w-4 h-4 mr-2" />
            Create Items
          </Button>
        </div>
      </div>

      {/* Search and Actions */}
      <div className="glass rounded-xl p-4">
        <div className="flex items-center justify-between space-x-4">
          <div className="flex-1 relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search inventory..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {selectedItems.length > 0 && (
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">{selectedItems.length} selected</Badge>
              <Button size="sm" variant="outline" onClick={handleProcessClick}>
                <ArrowRightLeft className="w-4 h-4 mr-1" />
                Process
              </Button>
              <Button size="sm" variant="outline" onClick={handleDeleteClick}>
                <Trash2 className="w-4 h-4 mr-1" />
                Delete
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="glass">
          <TabsTrigger value="tools" className="flex items-center space-x-2">
            <Wrench className="w-4 h-4" />
            <span>Tools</span>
            <Badge variant="secondary" className="ml-1">{inventoryData.tools.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="materials" className="flex items-center space-x-2">
            <Package className="w-4 h-4" />
            <span>Materials</span>
            <Badge variant="secondary" className="ml-1">{inventoryData.materials.length}</Badge>
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <div className="glass rounded-xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/50">
                  <tr>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground w-12">
                      <input
                        type="checkbox"
                        checked={selectedItems.length === filteredData.length && filteredData.length > 0}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedItems(filteredData);
                          } else {
                            setSelectedItems([]);
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                    </th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Name</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Category</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">
                      {activeTab === 'tools' ? 'Quantity' : 'Stock'}
                    </th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Status</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Location</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData.map((item) => {
                    const isSelected = selectedItems.some(selected => selected.id === item.id);
                    return (
                      <tr
                        key={item.id}
                        className={cn(
                          "border-b border-gray-100 hover:bg-white/50 transition-all-smooth",
                          isSelected && "bg-blue-50/50",
                          activeTab === 'materials' && (item as any).isLowStock && "bg-red-50/30"
                        )}
                      >
                        <td className="py-4 px-4">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => toggleItemSelection(item)}
                            className="rounded border-gray-300"
                          />
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(item.type, item)}
                            <div>
                              <p className="font-medium text-sm">{item.name}</p>
                              <p className="text-xs text-muted-foreground">ID: {item.id}</p>
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Badge variant="outline" className="text-xs">
                            {item.category.replace('-', ' ')}
                          </Badge>
                        </td>
                        <td className="py-4 px-4">
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium">
                                Total: {item.total} {(item as any).unit || 'pcs'}
                              </span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <CheckCircle className="w-3 h-3 text-green-500" />
                              <span className="text-xs text-green-600">
                                Available: {item.available} {(item as any).unit || 'pcs'}
                              </span>
                            </div>
                            {activeTab === 'tools' && (item as any).borrowed > 0 && (
                              <div className="flex items-center space-x-2">
                                <Clock className="w-3 h-3 text-blue-500" />
                                <span className="text-xs text-blue-600">
                                  Borrowed: {(item as any).borrowed} pcs
                                </span>
                              </div>
                            )}
                            {activeTab === 'tools' && (item as any).damaged > 0 && (
                              <div className="flex items-center space-x-2">
                                <AlertTriangle className="w-3 h-3 text-red-500" />
                                <span className="text-xs text-red-600">
                                  Damaged: {(item as any).damaged} pcs
                                </span>
                              </div>
                            )}
                            {activeTab === 'materials' && (item as any).consumed > 0 && (
                              <div className="flex items-center space-x-2">
                                <TrendingDown className="w-3 h-3 text-orange-500" />
                                <span className="text-xs text-orange-600">
                                  Consumed: {(item as any).consumed} {(item as any).unit}
                                </span>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="space-y-1">
                            {activeTab === 'tools' && getConditionBadge((item as any).condition)}
                            {activeTab === 'materials' && (item as any).isLowStock && (
                              <Badge className="bg-red-100 text-red-800 border-red-200 text-xs">
                                Low Stock
                              </Badge>
                            )}
                            {activeTab === 'materials' && !(item as any).isLowStock && (
                              <Badge className="bg-green-100 text-green-800 border-green-200 text-xs">
                                In Stock
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span className="text-sm">{item.location}</span>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center space-x-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleEditClick(item)}
                              className="hover-lift"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="hover-lift">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="hover-lift">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {filteredData.length === 0 && (
              <div className="text-center py-12">
                <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">No items found</h3>
                <p className="text-muted-foreground">
                  {searchQuery ? 'No items match your search criteria.' : 'No items in this category yet.'}
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Inventory Sidebar */}
      <InventorySidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        type={sidebarType}
        selectedItems={selectedItems}
        editItem={editItem}
        onSubmit={handleFormSubmit}
      />
    </div>
  );
}