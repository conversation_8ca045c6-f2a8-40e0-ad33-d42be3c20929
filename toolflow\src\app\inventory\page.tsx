'use client';

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { 
  Package, 
  Wrench, 
  Plus,
  Search,
  Filter,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

const inventoryItems = [
  {
    id: 1,
    name: "Kunci <PERSON>in Set",
    category: "Tools",
    quantity: 15,
    available: 12,
    borrowed: 3,
    condition: "good",
    location: "Warehouse A-1"
  },
  {
    id: 2,
    name: "Besi Beton 12mm",
    category: "Materials",
    quantity: 500,
    available: 450,
    consumed: 50,
    condition: "good",
    location: "Storage B-2",
    unit: "kg"
  },
  {
    id: 3,
    name: "Set Bor Listrik",
    category: "Tools",
    quantity: 8,
    available: 6,
    borrowed: 1,
    damaged: 1,
    condition: "mixed",
    location: "Warehouse A-2"
  },
  {
    id: 4,
    name: "Semen Portland",
    category: "Materials",
    quantity: 200,
    available: 180,
    consumed: 20,
    condition: "good",
    location: "Storage B-1",
    unit: "sak"
  },
  {
    id: 5,
    name: "Meteran Digital",
    category: "Tools",
    quantity: 20,
    available: 17,
    borrowed: 3,
    condition: "good",
    location: "Warehouse A-1"
  }
];

const getConditionBadge = (condition: string) => {
  switch (condition) {
    case 'good':
      return <Badge className="bg-green-100 text-green-800 border-green-200">Good</Badge>;
    case 'mixed':
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Mixed</Badge>;
    case 'poor':
      return <Badge className="bg-red-100 text-red-800 border-red-200">Poor</Badge>;
    default:
      return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Unknown</Badge>;
  }
};

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'Tools':
      return <Wrench className="w-5 h-5 text-primary" />;
    case 'Materials':
      return <Package className="w-5 h-5 text-primary" />;
    default:
      return <Package className="w-5 h-5 text-primary" />;
  }
};

export default function Inventory() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Tools & Materials</h1>
          <p className="text-muted-foreground mt-1">
            Manage your inventory of tools and materials
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" className="hover-lift">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button className="hover-lift">
            <Plus className="w-4 h-4 mr-2" />
            Add Item
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="glass rounded-xl p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <input
            type="text"
            placeholder="Search tools and materials..."
            className="w-full pl-10 pr-4 py-2 bg-transparent border border-white/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>
      </div>

      {/* Inventory Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {inventoryItems.map((item) => (
          <div key={item.id} className="glass rounded-xl p-6 hover-lift cursor-pointer transition-all-smooth hover:shadow-xl">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                {getCategoryIcon(item.category)}
                <div>
                  <h3 className="font-semibold text-foreground">{item.name}</h3>
                  <p className="text-sm text-muted-foreground">{item.category}</p>
                </div>
              </div>
              {getConditionBadge(item.condition)}
            </div>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Quantity</span>
                <span className="font-medium">
                  {item.quantity} {item.unit || 'pcs'}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Available</span>
                <div className="flex items-center space-x-1">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="font-medium text-green-600">
                    {item.available} {item.unit || 'pcs'}
                  </span>
                </div>
              </div>

              {item.borrowed && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Borrowed</span>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4 text-blue-500" />
                    <span className="font-medium text-blue-600">
                      {item.borrowed} {item.unit || 'pcs'}
                    </span>
                  </div>
                </div>
              )}

              {item.consumed && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Consumed</span>
                  <div className="flex items-center space-x-1">
                    <Package className="w-4 h-4 text-gray-500" />
                    <span className="font-medium text-gray-600">
                      {item.consumed} {item.unit || 'pcs'}
                    </span>
                  </div>
                </div>
              )}

              {item.damaged && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Damaged</span>
                  <div className="flex items-center space-x-1">
                    <AlertTriangle className="w-4 h-4 text-red-500" />
                    <span className="font-medium text-red-600">
                      {item.damaged} {item.unit || 'pcs'}
                    </span>
                  </div>
                </div>
              )}

              <div className="pt-2 border-t border-white/10">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Location</span>
                  <span className="text-sm font-medium">{item.location}</span>
                </div>
              </div>
            </div>

            <div className="mt-4 flex space-x-2">
              <Button variant="outline" size="sm" className="flex-1">
                Edit
              </Button>
              <Button variant="outline" size="sm" className="flex-1">
                History
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
