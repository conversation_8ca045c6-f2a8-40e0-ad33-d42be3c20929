import React, { useState, useEffect, useRef } from 'react';
import { KPICard } from '@/components/ui/kpi-card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Package,
  Wrench,
  ArrowRightLeft,
  TrendingDown,
  Clock,
  User,
  FileText,
  AlertTriangle,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

const kpiData = [
  {
    title: "Total Tools",
    value: "1,247",
    trend: { value: 12, isPositive: true },
    icon: <Wrench className="w-6 h-6 text-primary" />
  },
  {
    title: "Total Materials",
    value: "3,456",
    trend: { value: 8, isPositive: true },
    icon: <Package className="w-6 h-6 text-primary" />
  },
  {
    title: "Active Borrowings",
    value: "89",
    trend: { value: 15, isPositive: false },
    icon: <ArrowRightLeft className="w-6 h-6 text-primary" />
  },
  {
    title: "Consumed Materials",
    value: "234",
    trend: { value: 5, isPositive: true },
    icon: <TrendingDown className="w-6 h-6 text-primary" />
  },
  {
    title: "Poor Condition Returns",
    value: "12",
    trend: { value: 3, isPositive: false },
    icon: <AlertTriangle className="w-6 h-6 text-primary" />
  }
];

const recentActivities = [
  {
    time: "10:30 AM",
    activity: "Peminjaman Alat",
    user: "Budi Santoso",
    items: "Kunci Angin (x2)",
    status: "active"
  },
  {
    time: "09:45 AM",
    activity: "Konsumsi Material",
    user: "Sari Dewi",
    items: "Besi Beton (50kg)",
    status: "completed"
  },
  {
    time: "09:15 AM",
    activity: "Pengembalian Alat",
    user: "Ahmad Wijaya",
    items: "Set Bor (x1)",
    status: "completed"
  },
  {
    time: "08:50 AM",
    activity: "Peminjaman Alat",
    user: "Rina Kusuma",
    items: "Meteran (x3)",
    status: "active"
  },
  {
    time: "08:30 AM",
    activity: "Konsumsi Material",
    user: "Dedi Kurniawan",
    items: "Semen Cor (100kg)",
    status: "completed"
  }
];

const notifications = [
  {
    type: "overdue",
    title: "Pengembalian Terlambat",
    message: "Kunci Angin yang dipinjam Budi Santoso sudah terlambat 3 hari",
    priority: "high",
    time: "2 jam yang lalu"
  },
  {
    type: "low_stock",
    title: "Peringatan Stok Menipis",
    message: "Stok Besi Beton di bawah batas minimal (tersisa 5kg)",
    priority: "medium",
    time: "4 jam yang lalu"
  },
  {
    type: "damaged",
    title: "Laporan Barang Rusak",
    message: "Set Bor dikembalikan dalam kondisi buruk oleh Ahmad Wijaya",
    priority: "high",
    time: "6 jam yang lalu"
  },
  {
    type: "system",
    title: "Pembaruan Sistem",
    message: "Sinkronisasi inventori berhasil diselesaikan",
    priority: "low",
    time: "1 hari yang lalu"
  }
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'active': return <Clock className="w-4 h-4 text-blue-500" />;
    case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />;
    default: return <FileText className="w-4 h-4 text-gray-500" />;
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high': return 'bg-red-100 text-red-800 border-red-200';
    case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low': return 'bg-green-100 text-green-800 border-green-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("activities");
  const [currentSlide, setCurrentSlide] = useState(0);
  const sliderRef = useRef<HTMLDivElement>(null);

  // Auto-slide effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % kpiData.length);
    }, 3000); // Slide every 3 seconds

    return () => clearInterval(interval);
  }, []);

  // Update slider position
  useEffect(() => {
    if (sliderRef.current) {
      const cardWidth = 280 + 16; // card width + gap
      sliderRef.current.style.transform = `translateX(-${currentSlide * cardWidth}px)`;
    }
  }, [currentSlide]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground mt-1">
            Overview of your tools and materials management
          </p>
        </div>
        <Button className="hover-lift">
          <FileText className="w-4 h-4 mr-2" />
          Generate Report
        </Button>
      </div>

      {/* KPI Slider */}
      <div className="relative overflow-hidden">
        <div
          ref={sliderRef}
          className="flex space-x-4 transition-transform duration-500 ease-in-out"
          style={{ width: `${kpiData.length * 296}px` }}
        >
          {kpiData.map((kpi, index) => (
            <KPICard
              key={index}
              title={kpi.title}
              value={kpi.value}
              trend={kpi.trend}
              icon={kpi.icon}
            />
          ))}
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center space-x-2 mt-4">
          {kpiData.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={cn(
                "w-2 h-2 rounded-full transition-all duration-300",
                currentSlide === index
                  ? "bg-primary scale-125"
                  : "bg-gray-300 hover:bg-gray-400"
              )}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="glass">
          <TabsTrigger value="activities">Last 5 Activities</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="contextual">Contextual</TabsTrigger>
        </TabsList>

        <TabsContent value="activities" className="space-y-4">
          <div className="glass rounded-xl p-6">
            <h3 className="text-lg font-semibold mb-4">Recent Activities</h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">Time</th>
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">Activity</th>
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">User</th>
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">Item(s)</th>
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {recentActivities.map((activity, index) => (
                    <tr
                      key={index}
                      className="border-b border-gray-100 hover:bg-white/50 cursor-pointer transition-all-smooth"
                    >
                      <td className="py-3 px-4 text-sm">{activity.time}</td>
                      <td className="py-3 px-4 text-sm font-medium">{activity.activity}</td>
                      <td className="py-3 px-4 text-sm flex items-center">
                        <User className="w-4 h-4 mr-2 text-muted-foreground" />
                        {activity.user}
                      </td>
                      <td className="py-3 px-4 text-sm">{activity.items}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(activity.status)}
                          <span className="text-sm capitalize">{activity.status}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <div className="glass rounded-xl p-6">
            <h3 className="text-lg font-semibold mb-4">Notifications</h3>
            <div className="space-y-3">
              {notifications.map((notification, index) => (
                <div
                  key={index}
                  className="p-4 rounded-lg border transition-all-smooth hover:shadow-md glass"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-sm">{notification.title}</h4>
                        <Badge className={cn("text-xs", getPriorityColor(notification.priority))}>
                          {notification.priority}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {notification.message}
                      </p>
                      <p className="text-xs text-muted-foreground">{notification.time}</p>
                    </div>
                    <Button variant="outline" size="sm" className="ml-4">
                      Action
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="contextual" className="space-y-4">
          <div className="glass rounded-xl p-6">
            <h3 className="text-lg font-semibold mb-4">Last 7 Days Activity Chart</h3>
            <div className="h-64 flex items-center justify-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
              <div className="text-center">
                <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Chart visualization will be implemented here</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Activity trends, borrowing patterns, and consumption analytics
                </p>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
