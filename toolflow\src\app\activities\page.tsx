'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ContextualSidebar } from '@/components/ui/contextual-sidebar';
import { cn } from '@/lib/utils';
import {
  Activity,
  Search,
  Filter,
  Calendar,
  Package,
  User,
  AlertTriangle,
  Clock,
  CheckCircle,
  Eye,
  FileText,
  ArrowUpRight
} from 'lucide-react';
import Link from 'next/link';

const activeBorrowings = [
  {
    id: 'B001',
    borrower: '<PERSON><PERSON>',
    email: '<EMAIL>',
    items: [
      { id: 'T001', name: '<PERSON>nc<PERSON>', quantity: 10, originalCondition: 'good' },
      { id: 'T005', name: '<PERSON> Bo<PERSON>', quantity: 2, originalCondition: 'good' }
    ],
    dueDate: '2024-01-20',
    borrowDate: '2024-01-15',
    purpose: 'Proyek Konstruksi Gedung A',
    isOverdue: false,
    status: 'active'
  },
  {
    id: 'B002',
    borrower: 'Sari Dewi',
    email: '<EMAIL>',
    items: [
      { id: 'T003', name: 'Meteran', quantity: 5, originalCondition: 'good' }
    ],
    dueDate: '2024-01-12',
    borrowDate: '2024-01-08',
    purpose: 'Survei Lokasi Proyek',
    isOverdue: true,
    status: 'overdue'
  },
  {
    id: 'B003',
    borrower: 'Ahmad Wijaya',
    email: '<EMAIL>',
    items: [
      { id: 'T002', name: 'Gergaji Listrik', quantity: 1, originalCondition: 'good' },
      { id: 'T004', name: 'Perlengkapan Keselamatan', quantity: 3, originalCondition: 'good' }
    ],
    dueDate: '2024-01-25',
    borrowDate: '2024-01-18',
    purpose: 'Renovasi Workshop',
    isOverdue: false,
    status: 'active'
  }
];

const activeConsumptions = [
  {
    id: 'C001',
    consumer: 'Tim Proyek A',
    contact: '<EMAIL>',
    materials: [
      { id: 'M001', name: 'Besi Beton', quantity: 50, unit: 'kg', remainingQuantity: 125 },
      { id: 'M002', name: 'Semen Cor', quantity: 100, unit: 'kg', remainingQuantity: 400 }
    ],
    startDate: '2024-01-10',
    purpose: 'Pekerjaan Pondasi',
    status: 'ongoing',
    completionEstimate: '2024-01-30'
  },
  {
    id: 'C002',
    consumer: 'Tim Pemeliharaan',
    contact: '<EMAIL>',
    materials: [
      { id: 'M003', name: 'Kabel Listrik', quantity: 200, unit: 'meter', remainingQuantity: 800 }
    ],
    startDate: '2024-01-14',
    purpose: 'Pemeliharaan Instalasi Listrik',
    status: 'ongoing',
    completionEstimate: '2024-01-28'
  }
];

const transactionHistory = [
  {
    id: 'H001',
    type: 'return',
    user: 'Budi Santoso',
    items: 'Kunci Angin (x8), Mata Bor (x12)',
    date: '2024-01-14',
    status: 'completed',
    conditions: 'Baik: 18, Buruk: 2',
    notes: 'Beberapa kunci angin menunjukkan tanda aus karena pemakaian berat'
  },
  {
    id: 'H002',
    type: 'consume',
    user: 'Tim Proyek B',
    items: 'Besi Beton (75kg), Kawat Las (10kg)',
    date: '2024-01-13',
    status: 'completed',
    purpose: 'Kerangka Struktur Bangunan',
    totalValue: 'Rp 18.750.000'
  },
  {
    id: 'H003',
    type: 'return',
    user: 'Sari Dewi',
    items: 'Set Alat Listrik (x1)',
    date: '2024-01-12',
    status: 'completed',
    conditions: 'Baik: 1',
    notes: 'Dikembalikan dalam kondisi sempurna'
  },
  {
    id: 'H004',
    type: 'consume',
    user: 'Tim Pemeliharaan',
    items: 'Perlengkapan Kebersihan (Beragam)',
    date: '2024-01-11',
    status: 'completed',
    purpose: 'Pemeliharaan Fasilitas Bulanan',
    totalValue: 'Rp 2.775.000'
  }
];

export default function Activities() {
  const [activeTab, setActiveTab] = useState("borrowing");
  const [searchQuery, setSearchQuery] = useState("");
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarType, setSidebarType] = useState<'return' | 'extend'>('return');
  const [selectedBorrowing, setSelectedBorrowing] = useState<any>(null);
  const [expandedHistory, setExpandedHistory] = useState<string[]>([]);

  const handleReturnClick = (borrowing: any) => {
    setSelectedBorrowing(borrowing);
    setSidebarType('return');
    setSidebarOpen(true);
  };

  const handleExtendClick = (borrowing: any) => {
    setSelectedBorrowing(borrowing);
    setSidebarType('extend');
    setSidebarOpen(true);
  };

  const handleFormSubmit = (formData: any) => {
    console.log('Form submitted:', formData);
    // Handle form submission logic here
  };

  const toggleHistoryDetails = (id: string) => {
    setExpandedHistory(prev =>
      prev.includes(id)
        ? prev.filter(historyId => historyId !== id)
        : [...prev, id]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'overdue': return 'bg-red-100 text-red-800 border-red-200';
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'ongoing': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'overdue': return <AlertTriangle className="w-4 h-4" />;
      case 'active': return <Clock className="w-4 h-4" />;
      case 'ongoing': return <Activity className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Activities</h1>
          <p className="text-muted-foreground mt-1">
            Manage borrowing, consuming, and transaction history
          </p>
        </div>
        <Button asChild className="hover-lift">
          <Link href="/reports">
            <FileText className="w-4 h-4 mr-2" />
            Generate Report
          </Link>
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="glass rounded-xl p-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search activities..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </Button>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="glass">
          <TabsTrigger value="borrowing" className="flex items-center space-x-2">
            <Package className="w-4 h-4" />
            <span>Borrowing</span>
            <Badge variant="secondary" className="ml-1">{activeBorrowings.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="consuming" className="flex items-center space-x-2">
            <Activity className="w-4 h-4" />
            <span>Consuming</span>
            <Badge variant="secondary" className="ml-1">{activeConsumptions.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center space-x-2">
            <Clock className="w-4 h-4" />
            <span>History</span>
          </TabsTrigger>
        </TabsList>

        {/* Borrowing Tab */}
        <TabsContent value="borrowing" className="space-y-4">
          <div className="glass rounded-xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/50">
                  <tr>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Peminjam</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Item</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Jatuh Tempo</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Tujuan</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Status</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Aksi</th>
                  </tr>
                </thead>
                <tbody>
                  {activeBorrowings.map((borrowing) => (
                    <tr
                      key={borrowing.id}
                      className={cn(
                        "border-b border-gray-100 hover:bg-white/50 transition-all-smooth",
                        borrowing.isOverdue && "bg-red-50/50"
                      )}
                    >
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium text-sm">{borrowing.borrower}</p>
                            <p className="text-xs text-muted-foreground">{borrowing.email}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="space-y-1">
                          {borrowing.items.map((item, index) => (
                            <div key={index} className="text-sm">
                              <span className="font-medium">{item.name}</span>
                              <span className="text-muted-foreground"> (x{item.quantity})</span>
                            </div>
                          ))}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4 text-muted-foreground" />
                          <span className={cn(
                            "text-sm",
                            borrowing.isOverdue && "text-red-600 font-medium"
                          )}>
                            {borrowing.dueDate}
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className="text-sm">{borrowing.purpose}</span>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(borrowing.status)}
                          <Badge className={cn("text-xs", getStatusColor(borrowing.status))}>
                            {borrowing.isOverdue ? 'Overdue' : 'Active'}
                          </Badge>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleReturnClick(borrowing)}
                            className="hover-lift"
                          >
                            Return
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleExtendClick(borrowing)}
                            className="hover-lift"
                          >
                            Extend
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>

        {/* Consuming Tab */}
        <TabsContent value="consuming" className="space-y-4">
          <div className="glass rounded-xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/50">
                  <tr>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Konsumen</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Material</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Tanggal Mulai</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Tujuan</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {activeConsumptions.map((consumption) => (
                    <tr
                      key={consumption.id}
                      className="border-b border-gray-100 hover:bg-white/50 transition-all-smooth"
                    >
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium text-sm">{consumption.consumer}</p>
                            <p className="text-xs text-muted-foreground">{consumption.contact}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="space-y-1">
                          {consumption.materials.map((material, index) => (
                            <div key={index} className="text-sm">
                              <span className="font-medium">{material.name}</span>
                              <span className="text-muted-foreground"> ({material.quantity} {material.unit})</span>
                              <div className="text-xs text-blue-600">
                                Remaining: {material.remainingQuantity} {material.unit}
                              </div>
                            </div>
                          ))}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">{consumption.startDate}</span>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <span className="text-sm">{consumption.purpose}</span>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(consumption.status)}
                          <Badge className={cn("text-xs", getStatusColor(consumption.status))}>
                            {consumption.status}
                          </Badge>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Combined history of completed transactions (returns and consumptions)
            </p>
            <Button variant="outline" size="sm" asChild className="hover-lift">
              <Link href="/reports?preset=history">
                <FileText className="w-4 h-4 mr-2" />
                Generate History Report
                <ArrowUpRight className="w-3 h-3 ml-1" />
              </Link>
            </Button>
          </div>

          <div className="glass rounded-xl overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/50">
                  <tr>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Date</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Type</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">User</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Items</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Status</th>
                    <th className="text-left py-4 px-4 font-medium text-muted-foreground">Details</th>
                  </tr>
                </thead>
                <tbody>
                  {transactionHistory.map((transaction) => (
                    <React.Fragment key={transaction.id}>
                      <tr
                        className="border-b border-gray-100 hover:bg-white/50 cursor-pointer transition-all-smooth"
                        onClick={() => toggleHistoryDetails(transaction.id)}
                      >
                        <td className="py-4 px-4">
                          <div className="flex items-center space-x-2">
                            <Calendar className="w-4 h-4 text-muted-foreground" />
                            <span className="text-sm">{transaction.date}</span>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Badge className={cn(
                            "text-xs",
                            transaction.type === 'return'
                              ? "bg-blue-100 text-blue-800 border-blue-200"
                              : "bg-purple-100 text-purple-800 border-purple-200"
                          )}>
                            {transaction.type}
                          </Badge>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center space-x-2">
                            <User className="w-4 h-4 text-muted-foreground" />
                            <span className="text-sm font-medium">{transaction.user}</span>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span className="text-sm">{transaction.items}</span>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(transaction.status)}
                            <Badge className={cn("text-xs", getStatusColor(transaction.status))}>
                              {transaction.status}
                            </Badge>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Button variant="ghost" size="sm">
                            <Eye className="w-4 h-4" />
                          </Button>
                        </td>
                      </tr>

                      {expandedHistory.includes(transaction.id) && (
                        <tr>
                          <td colSpan={6} className="py-4 px-6 bg-gray-50/50">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <h4 className="font-medium text-sm mb-2">Transaction Details</h4>
                                <div className="space-y-1 text-sm">
                                  <p><strong>ID:</strong> {transaction.id}</p>
                                  <p><strong>Date:</strong> {transaction.date}</p>
                                  <p><strong>Type:</strong> {transaction.type}</p>
                                  {transaction.purpose && (
                                    <p><strong>Purpose:</strong> {transaction.purpose}</p>
                                  )}
                                  {transaction.totalValue && (
                                    <p><strong>Total Value:</strong> {transaction.totalValue}</p>
                                  )}
                                </div>
                              </div>
                              <div>
                                <h4 className="font-medium text-sm mb-2">Additional Information</h4>
                                <div className="space-y-1 text-sm">
                                  {transaction.conditions && (
                                    <p><strong>Conditions:</strong> {transaction.conditions}</p>
                                  )}
                                  {transaction.notes && (
                                    <p><strong>Notes:</strong> {transaction.notes}</p>
                                  )}
                                </div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Contextual Sidebar */}
      <ContextualSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        type={sidebarType}
        borrowing={selectedBorrowing}
        onSubmit={handleFormSubmit}
      />
    </div>
  );
}