'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { 
  ArrowRightLeft,
  TrendingDown,
  Clock,
  User,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar,
  Filter,
  Download
} from 'lucide-react';

const activities = [
  {
    id: 1,
    type: "borrow",
    user: "<PERSON><PERSON>o",
    items: "Kunci Angin Set (x2)",
    timestamp: "2024-01-15 10:30:00",
    status: "active",
    dueDate: "2024-01-20",
    location: "Project Site A"
  },
  {
    id: 2,
    type: "consume",
    user: "<PERSON>ri <PERSON>wi",
    items: "Besi Beton 12mm (50kg)",
    timestamp: "2024-01-15 09:45:00",
    status: "completed",
    location: "Project Site B"
  },
  {
    id: 3,
    type: "return",
    user: "<PERSON>",
    items: "Set Bor Listrik (x1)",
    timestamp: "2024-01-15 09:15:00",
    status: "completed",
    condition: "good",
    location: "Warehouse A-2"
  },
  {
    id: 4,
    type: "borrow",
    user: "Rina Kusuma",
    items: "Meteran Digital (x3)",
    timestamp: "2024-01-15 08:50:00",
    status: "active",
    dueDate: "2024-01-18",
    location: "Project Site C"
  },
  {
    id: 5,
    type: "return",
    user: "Dedi Kurniawan",
    items: "Kunci Angin Set (x1)",
    timestamp: "2024-01-15 08:30:00",
    status: "completed",
    condition: "damaged",
    location: "Warehouse A-1"
  },
  {
    id: 6,
    type: "consume",
    user: "Lisa Permata",
    items: "Semen Portland (10 sak)",
    timestamp: "2024-01-14 16:20:00",
    status: "completed",
    location: "Project Site A"
  }
];

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'borrow':
      return <ArrowRightLeft className="w-5 h-5 text-blue-500" />;
    case 'return':
      return <ArrowRightLeft className="w-5 h-5 text-green-500" />;
    case 'consume':
      return <TrendingDown className="w-5 h-5 text-orange-500" />;
    default:
      return <Clock className="w-5 h-5 text-gray-500" />;
  }
};

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'active':
      return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Active</Badge>;
    case 'completed':
      return <Badge className="bg-green-100 text-green-800 border-green-200">Completed</Badge>;
    case 'overdue':
      return <Badge className="bg-red-100 text-red-800 border-red-200">Overdue</Badge>;
    default:
      return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Unknown</Badge>;
  }
};

const getConditionBadge = (condition: string) => {
  switch (condition) {
    case 'good':
      return <Badge className="bg-green-100 text-green-800 border-green-200">Good</Badge>;
    case 'damaged':
      return <Badge className="bg-red-100 text-red-800 border-red-200">Damaged</Badge>;
    case 'poor':
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Poor</Badge>;
    default:
      return null;
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export default function Activities() {
  const [activeTab, setActiveTab] = useState("all");

  const filteredActivities = activities.filter(activity => {
    if (activeTab === "all") return true;
    return activity.type === activeTab;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Activities</h1>
          <p className="text-muted-foreground mt-1">
            Track all borrowing, returning, and consumption activities
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" className="hover-lift">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" className="hover-lift">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Activity Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="glass">
          <TabsTrigger value="all">All Activities</TabsTrigger>
          <TabsTrigger value="borrow">Borrowing</TabsTrigger>
          <TabsTrigger value="return">Returns</TabsTrigger>
          <TabsTrigger value="consume">Consumption</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <div className="glass rounded-xl p-6">
            <div className="space-y-4">
              {filteredActivities.map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-start space-x-4 p-4 rounded-lg border border-white/10 hover:bg-white/5 transition-all-smooth"
                >
                  <div className="flex-shrink-0 p-2 bg-white/10 rounded-lg">
                    {getActivityIcon(activity.type)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-medium text-foreground capitalize">
                            {activity.type === 'borrow' ? 'Borrowing' : 
                             activity.type === 'return' ? 'Return' : 'Consumption'}
                          </h3>
                          {getStatusBadge(activity.status)}
                          {activity.condition && getConditionBadge(activity.condition)}
                        </div>

                        <div className="flex items-center space-x-1 text-sm text-muted-foreground mb-2">
                          <User className="w-4 h-4" />
                          <span>{activity.user}</span>
                        </div>

                        <p className="text-sm font-medium text-foreground mb-2">
                          {activity.items}
                        </p>

                        <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-3 h-3" />
                            <span>{formatDate(activity.timestamp)}</span>
                          </div>
                          <span>•</span>
                          <span>{activity.location}</span>
                          {activity.dueDate && (
                            <>
                              <span>•</span>
                              <span>Due: {new Date(activity.dueDate).toLocaleDateString('id-ID')}</span>
                            </>
                          )}
                        </div>
                      </div>

                      <div className="flex space-x-2 ml-4">
                        <Button variant="outline" size="sm">
                          Details
                        </Button>
                        {activity.status === 'active' && activity.type === 'borrow' && (
                          <Button size="sm">
                            Process Return
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredActivities.length === 0 && (
              <div className="text-center py-12">
                <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">No activities found</h3>
                <p className="text-muted-foreground">
                  No activities match the current filter criteria.
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
