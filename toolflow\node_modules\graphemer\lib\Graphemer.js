"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const boundaries_1 = require("./boundaries");
const GraphemerHelper_1 = __importDefault(require("./GraphemerHelper"));
const GraphemerIterator_1 = __importDefault(require("./GraphemerIterator"));
class Graphemer {
    /**
     * Returns the next grapheme break in the string after the given index
     * @param string {string}
     * @param index {number}
     * @returns {number}
     */
    static nextBreak(string, index) {
        if (index === undefined) {
            index = 0;
        }
        if (index < 0) {
            return 0;
        }
        if (index >= string.length - 1) {
            return string.length;
        }
        const prevCP = GraphemerHelper_1.default.codePointAt(string, index);
        const prev = Graphemer.getGraphemeBreakProperty(prevCP);
        const prevEmoji = Graphemer.getEmojiProperty(prevCP);
        const mid = [];
        const midEmoji = [];
        for (let i = index + 1; i < string.length; i++) {
            // check for already processed low surrogates
            if (GraphemerHelper_1.default.isSurrogate(string, i - 1)) {
                continue;
            }
            const nextCP = GraphemerHelper_1.default.codePointAt(string, i);
            const next = Graphemer.getGraphemeBreakProperty(nextCP);
            const nextEmoji = Graphemer.getEmojiProperty(nextCP);
            if (GraphemerHelper_1.default.shouldBreak(prev, mid, next, prevEmoji, midEmoji, nextEmoji)) {
                return i;
            }
            mid.push(next);
            midEmoji.push(nextEmoji);
        }
        return string.length;
    }
    /**
     * Breaks the given string into an array of grapheme clusters
     * @param str {string}
     * @returns {string[]}
     */
    splitGraphemes(str) {
        const res = [];
        let index = 0;
        let brk;
        while ((brk = Graphemer.nextBreak(str, index)) < str.length) {
            res.push(str.slice(index, brk));
            index = brk;
        }
        if (index < str.length) {
            res.push(str.slice(index));
        }
        return res;
    }
    /**
     * Returns an iterator of grapheme clusters in the given string
     * @param str {string}
     * @returns {GraphemerIterator}
     */
    iterateGraphemes(str) {
        return new GraphemerIterator_1.default(str, Graphemer.nextBreak);
    }
    /**
     * Returns the number of grapheme clusters in the given string
     * @param str {string}
     * @returns {number}
     */
    countGraphemes(str) {
        let count = 0;
        let index = 0;
        let brk;
        while ((brk = Graphemer.nextBreak(str, index)) < str.length) {
            index = brk;
            count++;
        }
        if (index < str.length) {
            count++;
        }
        return count;
    }
    /**
     * Given a Unicode code point, determines this symbol's grapheme break property
     * @param code {number} Unicode code point
     * @returns {number}
     */
    static getGraphemeBreakProperty(code) {
        // Grapheme break property taken from:
        // https://www.unicode.org/Public/UCD/latest/ucd/auxiliary/GraphemeBreakProperty.txt
        // and generated by
        // node ./scripts/generate-grapheme-break.js
        if (code < 0xbf09) {
            if (code < 0xac54) {
                if (code < 0x102d) {
                    if (code < 0xb02) {
                        if (code < 0x93b) {
                            if (code < 0x6df) {
                                if (code < 0x5bf) {
                                    if (code < 0x7f) {
                                        if (code < 0xb) {
                                            if (code < 0xa) {
                                                // Cc  [10] <control-0000>..<control-0009>
                                                if (0x0 <= code && code <= 0x9) {
                                                    return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                }
                                            }
                                            else {
                                                // Cc       <control-000A>
                                                if (0xa === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LF;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xd) {
                                                // Cc   [2] <control-000B>..<control-000C>
                                                if (0xb <= code && code <= 0xc) {
                                                    return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                }
                                            }
                                            else {
                                                if (code < 0xe) {
                                                    // Cc       <control-000D>
                                                    if (0xd === code) {
                                                        return boundaries_1.CLUSTER_BREAK.CR;
                                                    }
                                                }
                                                else {
                                                    // Cc  [18] <control-000E>..<control-001F>
                                                    if (0xe <= code && code <= 0x1f) {
                                                        return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x300) {
                                            if (code < 0xad) {
                                                // Cc  [33] <control-007F>..<control-009F>
                                                if (0x7f <= code && code <= 0x9f) {
                                                    return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                }
                                            }
                                            else {
                                                // Cf       SOFT HYPHEN
                                                if (0xad === code) {
                                                    return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x483) {
                                                // Mn [112] COMBINING GRAVE ACCENT..COMBINING LATIN SMALL LETTER X
                                                if (0x300 <= code && code <= 0x36f) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x591) {
                                                    // Mn   [5] COMBINING CYRILLIC TITLO..COMBINING CYRILLIC POKRYTIE
                                                    // Me   [2] COMBINING CYRILLIC HUNDRED THOUSANDS SIGN..COMBINING CYRILLIC MILLIONS SIGN
                                                    if (0x483 <= code && code <= 0x489) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn  [45] HEBREW ACCENT ETNAHTA..HEBREW POINT METEG
                                                    if (0x591 <= code && code <= 0x5bd) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0x610) {
                                        if (code < 0x5c4) {
                                            if (code < 0x5c1) {
                                                // Mn       HEBREW POINT RAFE
                                                if (0x5bf === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn   [2] HEBREW POINT SHIN DOT..HEBREW POINT SIN DOT
                                                if (0x5c1 <= code && code <= 0x5c2) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x5c7) {
                                                // Mn   [2] HEBREW MARK UPPER DOT..HEBREW MARK LOWER DOT
                                                if (0x5c4 <= code && code <= 0x5c5) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x600) {
                                                    // Mn       HEBREW POINT QAMATS QATAN
                                                    if (0x5c7 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Cf   [6] ARABIC NUMBER SIGN..ARABIC NUMBER MARK ABOVE
                                                    if (0x600 <= code && code <= 0x605) {
                                                        return boundaries_1.CLUSTER_BREAK.PREPEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x670) {
                                            if (code < 0x61c) {
                                                // Mn  [11] ARABIC SIGN SALLALLAHOU ALAYHE WASSALLAM..ARABIC SMALL KASRA
                                                if (0x610 <= code && code <= 0x61a) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x64b) {
                                                    // Cf       ARABIC LETTER MARK
                                                    if (0x61c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                    }
                                                }
                                                else {
                                                    // Mn  [21] ARABIC FATHATAN..ARABIC WAVY HAMZA BELOW
                                                    if (0x64b <= code && code <= 0x65f) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x6d6) {
                                                // Mn       ARABIC LETTER SUPERSCRIPT ALEF
                                                if (0x670 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x6dd) {
                                                    // Mn   [7] ARABIC SMALL HIGH LIGATURE SAD WITH LAM WITH ALEF MAKSURA..ARABIC SMALL HIGH SEEN
                                                    if (0x6d6 <= code && code <= 0x6dc) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Cf       ARABIC END OF AYAH
                                                    if (0x6dd === code) {
                                                        return boundaries_1.CLUSTER_BREAK.PREPEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0x81b) {
                                    if (code < 0x730) {
                                        if (code < 0x6ea) {
                                            if (code < 0x6e7) {
                                                // Mn   [6] ARABIC SMALL HIGH ROUNDED ZERO..ARABIC SMALL HIGH MADDA
                                                if (0x6df <= code && code <= 0x6e4) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn   [2] ARABIC SMALL HIGH YEH..ARABIC SMALL HIGH NOON
                                                if (0x6e7 <= code && code <= 0x6e8) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x70f) {
                                                // Mn   [4] ARABIC EMPTY CENTRE LOW STOP..ARABIC SMALL LOW MEEM
                                                if (0x6ea <= code && code <= 0x6ed) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Cf       SYRIAC ABBREVIATION MARK
                                                if (0x70f === code) {
                                                    return boundaries_1.CLUSTER_BREAK.PREPEND;
                                                }
                                                // Mn       SYRIAC LETTER SUPERSCRIPT ALAPH
                                                if (0x711 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x7eb) {
                                            if (code < 0x7a6) {
                                                // Mn  [27] SYRIAC PTHAHA ABOVE..SYRIAC BARREKH
                                                if (0x730 <= code && code <= 0x74a) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn  [11] THAANA ABAFILI..THAANA SUKUN
                                                if (0x7a6 <= code && code <= 0x7b0) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x7fd) {
                                                // Mn   [9] NKO COMBINING SHORT HIGH TONE..NKO COMBINING DOUBLE DOT ABOVE
                                                if (0x7eb <= code && code <= 0x7f3) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x816) {
                                                    // Mn       NKO DANTAYALAN
                                                    if (0x7fd === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [4] SAMARITAN MARK IN..SAMARITAN MARK DAGESH
                                                    if (0x816 <= code && code <= 0x819) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0x898) {
                                        if (code < 0x829) {
                                            if (code < 0x825) {
                                                // Mn   [9] SAMARITAN MARK EPENTHETIC YUT..SAMARITAN VOWEL SIGN A
                                                if (0x81b <= code && code <= 0x823) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn   [3] SAMARITAN VOWEL SIGN SHORT A..SAMARITAN VOWEL SIGN U
                                                if (0x825 <= code && code <= 0x827) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x859) {
                                                // Mn   [5] SAMARITAN VOWEL SIGN LONG I..SAMARITAN MARK NEQUDAA
                                                if (0x829 <= code && code <= 0x82d) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x890) {
                                                    // Mn   [3] MANDAIC AFFRICATION MARK..MANDAIC GEMINATION MARK
                                                    if (0x859 <= code && code <= 0x85b) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Cf   [2] ARABIC POUND MARK ABOVE..ARABIC PIASTRE MARK ABOVE
                                                    if (0x890 <= code && code <= 0x891) {
                                                        return boundaries_1.CLUSTER_BREAK.PREPEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x8e3) {
                                            if (code < 0x8ca) {
                                                // Mn   [8] ARABIC SMALL HIGH WORD AL-JUZ..ARABIC HALF MADDA OVER MADDA
                                                if (0x898 <= code && code <= 0x89f) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x8e2) {
                                                    // Mn  [24] ARABIC SMALL HIGH FARSI YEH..ARABIC SMALL HIGH SIGN SAFHA
                                                    if (0x8ca <= code && code <= 0x8e1) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Cf       ARABIC DISPUTED END OF AYAH
                                                    if (0x8e2 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.PREPEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x903) {
                                                // Mn  [32] ARABIC TURNED DAMMA BELOW..DEVANAGARI SIGN ANUSVARA
                                                if (0x8e3 <= code && code <= 0x902) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mc       DEVANAGARI SIGN VISARGA
                                                if (0x903 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                                // Mn       DEVANAGARI VOWEL SIGN OE
                                                if (0x93a === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            if (code < 0xa01) {
                                if (code < 0x982) {
                                    if (code < 0x94d) {
                                        if (code < 0x93e) {
                                            // Mc       DEVANAGARI VOWEL SIGN OOE
                                            if (0x93b === code) {
                                                return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                            }
                                            // Mn       DEVANAGARI SIGN NUKTA
                                            if (0x93c === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                        }
                                        else {
                                            if (code < 0x941) {
                                                // Mc   [3] DEVANAGARI VOWEL SIGN AA..DEVANAGARI VOWEL SIGN II
                                                if (0x93e <= code && code <= 0x940) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0x949) {
                                                    // Mn   [8] DEVANAGARI VOWEL SIGN U..DEVANAGARI VOWEL SIGN AI
                                                    if (0x941 <= code && code <= 0x948) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [4] DEVANAGARI VOWEL SIGN CANDRA O..DEVANAGARI VOWEL SIGN AU
                                                    if (0x949 <= code && code <= 0x94c) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x951) {
                                            if (code < 0x94e) {
                                                // Mn       DEVANAGARI SIGN VIRAMA
                                                if (0x94d === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mc   [2] DEVANAGARI VOWEL SIGN PRISHTHAMATRA E..DEVANAGARI VOWEL SIGN AW
                                                if (0x94e <= code && code <= 0x94f) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x962) {
                                                // Mn   [7] DEVANAGARI STRESS SIGN UDATTA..DEVANAGARI VOWEL SIGN UUE
                                                if (0x951 <= code && code <= 0x957) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x981) {
                                                    // Mn   [2] DEVANAGARI VOWEL SIGN VOCALIC L..DEVANAGARI VOWEL SIGN VOCALIC LL
                                                    if (0x962 <= code && code <= 0x963) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn       BENGALI SIGN CANDRABINDU
                                                    if (0x981 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0x9c7) {
                                        if (code < 0x9be) {
                                            if (code < 0x9bc) {
                                                // Mc   [2] BENGALI SIGN ANUSVARA..BENGALI SIGN VISARGA
                                                if (0x982 <= code && code <= 0x983) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn       BENGALI SIGN NUKTA
                                                if (0x9bc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x9bf) {
                                                // Mc       BENGALI VOWEL SIGN AA
                                                if (0x9be === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x9c1) {
                                                    // Mc   [2] BENGALI VOWEL SIGN I..BENGALI VOWEL SIGN II
                                                    if (0x9bf <= code && code <= 0x9c0) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn   [4] BENGALI VOWEL SIGN U..BENGALI VOWEL SIGN VOCALIC RR
                                                    if (0x9c1 <= code && code <= 0x9c4) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x9d7) {
                                            if (code < 0x9cb) {
                                                // Mc   [2] BENGALI VOWEL SIGN E..BENGALI VOWEL SIGN AI
                                                if (0x9c7 <= code && code <= 0x9c8) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0x9cd) {
                                                    // Mc   [2] BENGALI VOWEL SIGN O..BENGALI VOWEL SIGN AU
                                                    if (0x9cb <= code && code <= 0x9cc) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn       BENGALI SIGN VIRAMA
                                                    if (0x9cd === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x9e2) {
                                                // Mc       BENGALI AU LENGTH MARK
                                                if (0x9d7 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x9fe) {
                                                    // Mn   [2] BENGALI VOWEL SIGN VOCALIC L..BENGALI VOWEL SIGN VOCALIC LL
                                                    if (0x9e2 <= code && code <= 0x9e3) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn       BENGALI SANDHI MARK
                                                    if (0x9fe === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xa83) {
                                    if (code < 0xa47) {
                                        if (code < 0xa3c) {
                                            if (code < 0xa03) {
                                                // Mn   [2] GURMUKHI SIGN ADAK BINDI..GURMUKHI SIGN BINDI
                                                if (0xa01 <= code && code <= 0xa02) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mc       GURMUKHI SIGN VISARGA
                                                if (0xa03 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xa3e) {
                                                // Mn       GURMUKHI SIGN NUKTA
                                                if (0xa3c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xa41) {
                                                    // Mc   [3] GURMUKHI VOWEL SIGN AA..GURMUKHI VOWEL SIGN II
                                                    if (0xa3e <= code && code <= 0xa40) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] GURMUKHI VOWEL SIGN U..GURMUKHI VOWEL SIGN UU
                                                    if (0xa41 <= code && code <= 0xa42) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xa70) {
                                            if (code < 0xa4b) {
                                                // Mn   [2] GURMUKHI VOWEL SIGN EE..GURMUKHI VOWEL SIGN AI
                                                if (0xa47 <= code && code <= 0xa48) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xa51) {
                                                    // Mn   [3] GURMUKHI VOWEL SIGN OO..GURMUKHI SIGN VIRAMA
                                                    if (0xa4b <= code && code <= 0xa4d) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn       GURMUKHI SIGN UDAAT
                                                    if (0xa51 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xa75) {
                                                // Mn   [2] GURMUKHI TIPPI..GURMUKHI ADDAK
                                                if (0xa70 <= code && code <= 0xa71) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xa81) {
                                                    // Mn       GURMUKHI SIGN YAKASH
                                                    if (0xa75 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] GUJARATI SIGN CANDRABINDU..GUJARATI SIGN ANUSVARA
                                                    if (0xa81 <= code && code <= 0xa82) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xac9) {
                                        if (code < 0xabe) {
                                            // Mc       GUJARATI SIGN VISARGA
                                            if (0xa83 === code) {
                                                return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                            }
                                            // Mn       GUJARATI SIGN NUKTA
                                            if (0xabc === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                        }
                                        else {
                                            if (code < 0xac1) {
                                                // Mc   [3] GUJARATI VOWEL SIGN AA..GUJARATI VOWEL SIGN II
                                                if (0xabe <= code && code <= 0xac0) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xac7) {
                                                    // Mn   [5] GUJARATI VOWEL SIGN U..GUJARATI VOWEL SIGN CANDRA E
                                                    if (0xac1 <= code && code <= 0xac5) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] GUJARATI VOWEL SIGN E..GUJARATI VOWEL SIGN AI
                                                    if (0xac7 <= code && code <= 0xac8) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xae2) {
                                            if (code < 0xacb) {
                                                // Mc       GUJARATI VOWEL SIGN CANDRA O
                                                if (0xac9 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xacd) {
                                                    // Mc   [2] GUJARATI VOWEL SIGN O..GUJARATI VOWEL SIGN AU
                                                    if (0xacb <= code && code <= 0xacc) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn       GUJARATI SIGN VIRAMA
                                                    if (0xacd === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xafa) {
                                                // Mn   [2] GUJARATI VOWEL SIGN VOCALIC L..GUJARATI VOWEL SIGN VOCALIC LL
                                                if (0xae2 <= code && code <= 0xae3) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xb01) {
                                                    // Mn   [6] GUJARATI SIGN SUKUN..GUJARATI SIGN TWO-CIRCLE NUKTA ABOVE
                                                    if (0xafa <= code && code <= 0xaff) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn       ORIYA SIGN CANDRABINDU
                                                    if (0xb01 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else {
                        if (code < 0xcf3) {
                            if (code < 0xc04) {
                                if (code < 0xb82) {
                                    if (code < 0xb47) {
                                        if (code < 0xb3e) {
                                            if (code < 0xb3c) {
                                                // Mc   [2] ORIYA SIGN ANUSVARA..ORIYA SIGN VISARGA
                                                if (0xb02 <= code && code <= 0xb03) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn       ORIYA SIGN NUKTA
                                                if (0xb3c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb40) {
                                                // Mc       ORIYA VOWEL SIGN AA
                                                // Mn       ORIYA VOWEL SIGN I
                                                if (0xb3e <= code && code <= 0xb3f) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xb41) {
                                                    // Mc       ORIYA VOWEL SIGN II
                                                    if (0xb40 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn   [4] ORIYA VOWEL SIGN U..ORIYA VOWEL SIGN VOCALIC RR
                                                    if (0xb41 <= code && code <= 0xb44) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb4d) {
                                            if (code < 0xb4b) {
                                                // Mc   [2] ORIYA VOWEL SIGN E..ORIYA VOWEL SIGN AI
                                                if (0xb47 <= code && code <= 0xb48) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mc   [2] ORIYA VOWEL SIGN O..ORIYA VOWEL SIGN AU
                                                if (0xb4b <= code && code <= 0xb4c) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb55) {
                                                // Mn       ORIYA SIGN VIRAMA
                                                if (0xb4d === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xb62) {
                                                    // Mn   [2] ORIYA SIGN OVERLINE..ORIYA AI LENGTH MARK
                                                    // Mc       ORIYA AU LENGTH MARK
                                                    if (0xb55 <= code && code <= 0xb57) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] ORIYA VOWEL SIGN VOCALIC L..ORIYA VOWEL SIGN VOCALIC LL
                                                    if (0xb62 <= code && code <= 0xb63) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xbc6) {
                                        if (code < 0xbbf) {
                                            // Mn       TAMIL SIGN ANUSVARA
                                            if (0xb82 === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                            // Mc       TAMIL VOWEL SIGN AA
                                            if (0xbbe === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                        }
                                        else {
                                            if (code < 0xbc0) {
                                                // Mc       TAMIL VOWEL SIGN I
                                                if (0xbbf === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xbc1) {
                                                    // Mn       TAMIL VOWEL SIGN II
                                                    if (0xbc0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] TAMIL VOWEL SIGN U..TAMIL VOWEL SIGN UU
                                                    if (0xbc1 <= code && code <= 0xbc2) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xbd7) {
                                            if (code < 0xbca) {
                                                // Mc   [3] TAMIL VOWEL SIGN E..TAMIL VOWEL SIGN AI
                                                if (0xbc6 <= code && code <= 0xbc8) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xbcd) {
                                                    // Mc   [3] TAMIL VOWEL SIGN O..TAMIL VOWEL SIGN AU
                                                    if (0xbca <= code && code <= 0xbcc) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn       TAMIL SIGN VIRAMA
                                                    if (0xbcd === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc00) {
                                                // Mc       TAMIL AU LENGTH MARK
                                                if (0xbd7 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xc01) {
                                                    // Mn       TELUGU SIGN COMBINING CANDRABINDU ABOVE
                                                    if (0xc00 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [3] TELUGU SIGN CANDRABINDU..TELUGU SIGN VISARGA
                                                    if (0xc01 <= code && code <= 0xc03) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xcbe) {
                                    if (code < 0xc4a) {
                                        if (code < 0xc3e) {
                                            // Mn       TELUGU SIGN COMBINING ANUSVARA ABOVE
                                            if (0xc04 === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                            // Mn       TELUGU SIGN NUKTA
                                            if (0xc3c === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                        }
                                        else {
                                            if (code < 0xc41) {
                                                // Mn   [3] TELUGU VOWEL SIGN AA..TELUGU VOWEL SIGN II
                                                if (0xc3e <= code && code <= 0xc40) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xc46) {
                                                    // Mc   [4] TELUGU VOWEL SIGN U..TELUGU VOWEL SIGN VOCALIC RR
                                                    if (0xc41 <= code && code <= 0xc44) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn   [3] TELUGU VOWEL SIGN E..TELUGU VOWEL SIGN AI
                                                    if (0xc46 <= code && code <= 0xc48) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc81) {
                                            if (code < 0xc55) {
                                                // Mn   [4] TELUGU VOWEL SIGN O..TELUGU SIGN VIRAMA
                                                if (0xc4a <= code && code <= 0xc4d) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xc62) {
                                                    // Mn   [2] TELUGU LENGTH MARK..TELUGU AI LENGTH MARK
                                                    if (0xc55 <= code && code <= 0xc56) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] TELUGU VOWEL SIGN VOCALIC L..TELUGU VOWEL SIGN VOCALIC LL
                                                    if (0xc62 <= code && code <= 0xc63) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc82) {
                                                // Mn       KANNADA SIGN CANDRABINDU
                                                if (0xc81 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xcbc) {
                                                    // Mc   [2] KANNADA SIGN ANUSVARA..KANNADA SIGN VISARGA
                                                    if (0xc82 <= code && code <= 0xc83) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn       KANNADA SIGN NUKTA
                                                    if (0xcbc === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xcc6) {
                                        if (code < 0xcc0) {
                                            // Mc       KANNADA VOWEL SIGN AA
                                            if (0xcbe === code) {
                                                return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                            }
                                            // Mn       KANNADA VOWEL SIGN I
                                            if (0xcbf === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                        }
                                        else {
                                            if (code < 0xcc2) {
                                                // Mc   [2] KANNADA VOWEL SIGN II..KANNADA VOWEL SIGN U
                                                if (0xcc0 <= code && code <= 0xcc1) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xcc3) {
                                                    // Mc       KANNADA VOWEL SIGN UU
                                                    if (0xcc2 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] KANNADA VOWEL SIGN VOCALIC R..KANNADA VOWEL SIGN VOCALIC RR
                                                    if (0xcc3 <= code && code <= 0xcc4) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xccc) {
                                            if (code < 0xcc7) {
                                                // Mn       KANNADA VOWEL SIGN E
                                                if (0xcc6 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xcca) {
                                                    // Mc   [2] KANNADA VOWEL SIGN EE..KANNADA VOWEL SIGN AI
                                                    if (0xcc7 <= code && code <= 0xcc8) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] KANNADA VOWEL SIGN O..KANNADA VOWEL SIGN OO
                                                    if (0xcca <= code && code <= 0xccb) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xcd5) {
                                                // Mn   [2] KANNADA VOWEL SIGN AU..KANNADA SIGN VIRAMA
                                                if (0xccc <= code && code <= 0xccd) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xce2) {
                                                    // Mc   [2] KANNADA LENGTH MARK..KANNADA AI LENGTH MARK
                                                    if (0xcd5 <= code && code <= 0xcd6) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] KANNADA VOWEL SIGN VOCALIC L..KANNADA VOWEL SIGN VOCALIC LL
                                                    if (0xce2 <= code && code <= 0xce3) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            if (code < 0xddf) {
                                if (code < 0xd4e) {
                                    if (code < 0xd3f) {
                                        if (code < 0xd02) {
                                            if (code < 0xd00) {
                                                // Mc       KANNADA SIGN COMBINING ANUSVARA ABOVE RIGHT
                                                if (0xcf3 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn   [2] MALAYALAM SIGN COMBINING ANUSVARA ABOVE..MALAYALAM SIGN CANDRABINDU
                                                if (0xd00 <= code && code <= 0xd01) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xd3b) {
                                                // Mc   [2] MALAYALAM SIGN ANUSVARA..MALAYALAM SIGN VISARGA
                                                if (0xd02 <= code && code <= 0xd03) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xd3e) {
                                                    // Mn   [2] MALAYALAM SIGN VERTICAL BAR VIRAMA..MALAYALAM SIGN CIRCULAR VIRAMA
                                                    if (0xd3b <= code && code <= 0xd3c) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc       MALAYALAM VOWEL SIGN AA
                                                    if (0xd3e === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xd46) {
                                            if (code < 0xd41) {
                                                // Mc   [2] MALAYALAM VOWEL SIGN I..MALAYALAM VOWEL SIGN II
                                                if (0xd3f <= code && code <= 0xd40) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn   [4] MALAYALAM VOWEL SIGN U..MALAYALAM VOWEL SIGN VOCALIC RR
                                                if (0xd41 <= code && code <= 0xd44) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xd4a) {
                                                // Mc   [3] MALAYALAM VOWEL SIGN E..MALAYALAM VOWEL SIGN AI
                                                if (0xd46 <= code && code <= 0xd48) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xd4d) {
                                                    // Mc   [3] MALAYALAM VOWEL SIGN O..MALAYALAM VOWEL SIGN AU
                                                    if (0xd4a <= code && code <= 0xd4c) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn       MALAYALAM SIGN VIRAMA
                                                    if (0xd4d === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xdca) {
                                        if (code < 0xd62) {
                                            // Lo       MALAYALAM LETTER DOT REPH
                                            if (0xd4e === code) {
                                                return boundaries_1.CLUSTER_BREAK.PREPEND;
                                            }
                                            // Mc       MALAYALAM AU LENGTH MARK
                                            if (0xd57 === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                        }
                                        else {
                                            if (code < 0xd81) {
                                                // Mn   [2] MALAYALAM VOWEL SIGN VOCALIC L..MALAYALAM VOWEL SIGN VOCALIC LL
                                                if (0xd62 <= code && code <= 0xd63) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xd82) {
                                                    // Mn       SINHALA SIGN CANDRABINDU
                                                    if (0xd81 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] SINHALA SIGN ANUSVARAYA..SINHALA SIGN VISARGAYA
                                                    if (0xd82 <= code && code <= 0xd83) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xdd2) {
                                            if (code < 0xdcf) {
                                                // Mn       SINHALA SIGN AL-LAKUNA
                                                if (0xdca === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xdd0) {
                                                    // Mc       SINHALA VOWEL SIGN AELA-PILLA
                                                    if (0xdcf === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] SINHALA VOWEL SIGN KETTI AEDA-PILLA..SINHALA VOWEL SIGN DIGA AEDA-PILLA
                                                    if (0xdd0 <= code && code <= 0xdd1) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xdd6) {
                                                // Mn   [3] SINHALA VOWEL SIGN KETTI IS-PILLA..SINHALA VOWEL SIGN KETTI PAA-PILLA
                                                if (0xdd2 <= code && code <= 0xdd4) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xdd8) {
                                                    // Mn       SINHALA VOWEL SIGN DIGA PAA-PILLA
                                                    if (0xdd6 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [7] SINHALA VOWEL SIGN GAETTA-PILLA..SINHALA VOWEL SIGN KOMBUVA HAA GAYANUKITTA
                                                    if (0xdd8 <= code && code <= 0xdde) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xf35) {
                                    if (code < 0xe47) {
                                        if (code < 0xe31) {
                                            if (code < 0xdf2) {
                                                // Mc       SINHALA VOWEL SIGN GAYANUKITTA
                                                if (0xddf === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mc   [2] SINHALA VOWEL SIGN DIGA GAETTA-PILLA..SINHALA VOWEL SIGN DIGA GAYANUKITTA
                                                if (0xdf2 <= code && code <= 0xdf3) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xe33) {
                                                // Mn       THAI CHARACTER MAI HAN-AKAT
                                                if (0xe31 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xe34) {
                                                    // Lo       THAI CHARACTER SARA AM
                                                    if (0xe33 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn   [7] THAI CHARACTER SARA I..THAI CHARACTER PHINTHU
                                                    if (0xe34 <= code && code <= 0xe3a) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xeb4) {
                                            if (code < 0xeb1) {
                                                // Mn   [8] THAI CHARACTER MAITAIKHU..THAI CHARACTER YAMAKKAN
                                                if (0xe47 <= code && code <= 0xe4e) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn       LAO VOWEL SIGN MAI KAN
                                                if (0xeb1 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                                // Lo       LAO VOWEL SIGN AM
                                                if (0xeb3 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xec8) {
                                                // Mn   [9] LAO VOWEL SIGN I..LAO SEMIVOWEL SIGN LO
                                                if (0xeb4 <= code && code <= 0xebc) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xf18) {
                                                    // Mn   [7] LAO TONE MAI EK..LAO YAMAKKAN
                                                    if (0xec8 <= code && code <= 0xece) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] TIBETAN ASTROLOGICAL SIGN -KHYUD PA..TIBETAN ASTROLOGICAL SIGN SDONG TSHUGS
                                                    if (0xf18 <= code && code <= 0xf19) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xf7f) {
                                        if (code < 0xf39) {
                                            // Mn       TIBETAN MARK NGAS BZUNG NYI ZLA
                                            if (0xf35 === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                            // Mn       TIBETAN MARK NGAS BZUNG SGOR RTAGS
                                            if (0xf37 === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                        }
                                        else {
                                            if (code < 0xf3e) {
                                                // Mn       TIBETAN MARK TSA -PHRU
                                                if (0xf39 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xf71) {
                                                    // Mc   [2] TIBETAN SIGN YAR TSHES..TIBETAN SIGN MAR TSHES
                                                    if (0xf3e <= code && code <= 0xf3f) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn  [14] TIBETAN VOWEL SIGN AA..TIBETAN SIGN RJES SU NGA RO
                                                    if (0xf71 <= code && code <= 0xf7e) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xf8d) {
                                            if (code < 0xf80) {
                                                // Mc       TIBETAN SIGN RNAM BCAD
                                                if (0xf7f === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xf86) {
                                                    // Mn   [5] TIBETAN VOWEL SIGN REVERSED I..TIBETAN MARK HALANTA
                                                    if (0xf80 <= code && code <= 0xf84) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] TIBETAN SIGN LCI RTAGS..TIBETAN SIGN YANG RTAGS
                                                    if (0xf86 <= code && code <= 0xf87) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xf99) {
                                                // Mn  [11] TIBETAN SUBJOINED SIGN LCE TSA CAN..TIBETAN SUBJOINED LETTER JA
                                                if (0xf8d <= code && code <= 0xf97) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xfc6) {
                                                    // Mn  [36] TIBETAN SUBJOINED LETTER NYA..TIBETAN SUBJOINED LETTER FIXED-FORM RA
                                                    if (0xf99 <= code && code <= 0xfbc) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn       TIBETAN SYMBOL PADMA GDAN
                                                    if (0xfc6 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else {
                    if (code < 0x1c24) {
                        if (code < 0x1930) {
                            if (code < 0x1732) {
                                if (code < 0x1082) {
                                    if (code < 0x103d) {
                                        if (code < 0x1032) {
                                            if (code < 0x1031) {
                                                // Mn   [4] MYANMAR VOWEL SIGN I..MYANMAR VOWEL SIGN UU
                                                if (0x102d <= code && code <= 0x1030) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mc       MYANMAR VOWEL SIGN E
                                                if (0x1031 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1039) {
                                                // Mn   [6] MYANMAR VOWEL SIGN AI..MYANMAR SIGN DOT BELOW
                                                if (0x1032 <= code && code <= 0x1037) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x103b) {
                                                    // Mn   [2] MYANMAR SIGN VIRAMA..MYANMAR SIGN ASAT
                                                    if (0x1039 <= code && code <= 0x103a) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] MYANMAR CONSONANT SIGN MEDIAL YA..MYANMAR CONSONANT SIGN MEDIAL RA
                                                    if (0x103b <= code && code <= 0x103c) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x1058) {
                                            if (code < 0x1056) {
                                                // Mn   [2] MYANMAR CONSONANT SIGN MEDIAL WA..MYANMAR CONSONANT SIGN MEDIAL HA
                                                if (0x103d <= code && code <= 0x103e) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mc   [2] MYANMAR VOWEL SIGN VOCALIC R..MYANMAR VOWEL SIGN VOCALIC RR
                                                if (0x1056 <= code && code <= 0x1057) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x105e) {
                                                // Mn   [2] MYANMAR VOWEL SIGN VOCALIC L..MYANMAR VOWEL SIGN VOCALIC LL
                                                if (0x1058 <= code && code <= 0x1059) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x1071) {
                                                    // Mn   [3] MYANMAR CONSONANT SIGN MON MEDIAL NA..MYANMAR CONSONANT SIGN MON MEDIAL LA
                                                    if (0x105e <= code && code <= 0x1060) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [4] MYANMAR VOWEL SIGN GEBA KAREN I..MYANMAR VOWEL SIGN KAYAH EE
                                                    if (0x1071 <= code && code <= 0x1074) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0x1100) {
                                        if (code < 0x1085) {
                                            // Mn       MYANMAR CONSONANT SIGN SHAN MEDIAL WA
                                            if (0x1082 === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                            // Mc       MYANMAR VOWEL SIGN SHAN E
                                            if (0x1084 === code) {
                                                return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                            }
                                        }
                                        else {
                                            if (code < 0x108d) {
                                                // Mn   [2] MYANMAR VOWEL SIGN SHAN E ABOVE..MYANMAR VOWEL SIGN SHAN FINAL Y
                                                if (0x1085 <= code && code <= 0x1086) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn       MYANMAR SIGN SHAN COUNCIL EMPHATIC TONE
                                                if (0x108d === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                                // Mn       MYANMAR VOWEL SIGN AITON AI
                                                if (0x109d === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x135d) {
                                            if (code < 0x1160) {
                                                // Lo  [96] HANGUL CHOSEONG KIYEOK..HANGUL CHOSEONG FILLER
                                                if (0x1100 <= code && code <= 0x115f) {
                                                    return boundaries_1.CLUSTER_BREAK.L;
                                                }
                                            }
                                            else {
                                                if (code < 0x11a8) {
                                                    // Lo  [72] HANGUL JUNGSEONG FILLER..HANGUL JUNGSEONG O-YAE
                                                    if (0x1160 <= code && code <= 0x11a7) {
                                                        return boundaries_1.CLUSTER_BREAK.V;
                                                    }
                                                }
                                                else {
                                                    // Lo  [88] HANGUL JONGSEONG KIYEOK..HANGUL JONGSEONG SSANGNIEUN
                                                    if (0x11a8 <= code && code <= 0x11ff) {
                                                        return boundaries_1.CLUSTER_BREAK.T;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1712) {
                                                // Mn   [3] ETHIOPIC COMBINING GEMINATION AND VOWEL LENGTH MARK..ETHIOPIC COMBINING GEMINATION MARK
                                                if (0x135d <= code && code <= 0x135f) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x1715) {
                                                    // Mn   [3] TAGALOG VOWEL SIGN I..TAGALOG SIGN VIRAMA
                                                    if (0x1712 <= code && code <= 0x1714) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc       TAGALOG SIGN PAMUDPOD
                                                    if (0x1715 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0x17c9) {
                                    if (code < 0x17b6) {
                                        if (code < 0x1752) {
                                            if (code < 0x1734) {
                                                // Mn   [2] HANUNOO VOWEL SIGN I..HANUNOO VOWEL SIGN U
                                                if (0x1732 <= code && code <= 0x1733) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mc       HANUNOO SIGN PAMUDPOD
                                                if (0x1734 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1772) {
                                                // Mn   [2] BUHID VOWEL SIGN I..BUHID VOWEL SIGN U
                                                if (0x1752 <= code && code <= 0x1753) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x17b4) {
                                                    // Mn   [2] TAGBANWA VOWEL SIGN I..TAGBANWA VOWEL SIGN U
                                                    if (0x1772 <= code && code <= 0x1773) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] KHMER VOWEL INHERENT AQ..KHMER VOWEL INHERENT AA
                                                    if (0x17b4 <= code && code <= 0x17b5) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x17be) {
                                            if (code < 0x17b7) {
                                                // Mc       KHMER VOWEL SIGN AA
                                                if (0x17b6 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn   [7] KHMER VOWEL SIGN I..KHMER VOWEL SIGN UA
                                                if (0x17b7 <= code && code <= 0x17bd) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x17c6) {
                                                // Mc   [8] KHMER VOWEL SIGN OE..KHMER VOWEL SIGN AU
                                                if (0x17be <= code && code <= 0x17c5) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0x17c7) {
                                                    // Mn       KHMER SIGN NIKAHIT
                                                    if (0x17c6 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] KHMER SIGN REAHMUK..KHMER SIGN YUUKALEAPINTU
                                                    if (0x17c7 <= code && code <= 0x17c8) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0x1885) {
                                        if (code < 0x180b) {
                                            if (code < 0x17dd) {
                                                // Mn  [11] KHMER SIGN MUUSIKATOAN..KHMER SIGN BATHAMASAT
                                                if (0x17c9 <= code && code <= 0x17d3) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn       KHMER SIGN ATTHACAN
                                                if (0x17dd === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x180e) {
                                                // Mn   [3] MONGOLIAN FREE VARIATION SELECTOR ONE..MONGOLIAN FREE VARIATION SELECTOR THREE
                                                if (0x180b <= code && code <= 0x180d) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Cf       MONGOLIAN VOWEL SEPARATOR
                                                if (0x180e === code) {
                                                    return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                }
                                                // Mn       MONGOLIAN FREE VARIATION SELECTOR FOUR
                                                if (0x180f === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x1923) {
                                            if (code < 0x18a9) {
                                                // Mn   [2] MONGOLIAN LETTER ALI GALI BALUDA..MONGOLIAN LETTER ALI GALI THREE BALUDA
                                                if (0x1885 <= code && code <= 0x1886) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x1920) {
                                                    // Mn       MONGOLIAN LETTER ALI GALI DAGALGA
                                                    if (0x18a9 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [3] LIMBU VOWEL SIGN A..LIMBU VOWEL SIGN U
                                                    if (0x1920 <= code && code <= 0x1922) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1927) {
                                                // Mc   [4] LIMBU VOWEL SIGN EE..LIMBU VOWEL SIGN AU
                                                if (0x1923 <= code && code <= 0x1926) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0x1929) {
                                                    // Mn   [2] LIMBU VOWEL SIGN E..LIMBU VOWEL SIGN O
                                                    if (0x1927 <= code && code <= 0x1928) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [3] LIMBU SUBJOINED LETTER YA..LIMBU SUBJOINED LETTER WA
                                                    if (0x1929 <= code && code <= 0x192b) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            if (code < 0x1b3b) {
                                if (code < 0x1a58) {
                                    if (code < 0x1a19) {
                                        if (code < 0x1933) {
                                            if (code < 0x1932) {
                                                // Mc   [2] LIMBU SMALL LETTER KA..LIMBU SMALL LETTER NGA
                                                if (0x1930 <= code && code <= 0x1931) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn       LIMBU SMALL LETTER ANUSVARA
                                                if (0x1932 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1939) {
                                                // Mc   [6] LIMBU SMALL LETTER TA..LIMBU SMALL LETTER LA
                                                if (0x1933 <= code && code <= 0x1938) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0x1a17) {
                                                    // Mn   [3] LIMBU SIGN MUKPHRENG..LIMBU SIGN SA-I
                                                    if (0x1939 <= code && code <= 0x193b) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] BUGINESE VOWEL SIGN I..BUGINESE VOWEL SIGN U
                                                    if (0x1a17 <= code && code <= 0x1a18) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x1a55) {
                                            if (code < 0x1a1b) {
                                                // Mc   [2] BUGINESE VOWEL SIGN E..BUGINESE VOWEL SIGN O
                                                if (0x1a19 <= code && code <= 0x1a1a) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn       BUGINESE VOWEL SIGN AE
                                                if (0x1a1b === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1a56) {
                                                // Mc       TAI THAM CONSONANT SIGN MEDIAL RA
                                                if (0x1a55 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn       TAI THAM CONSONANT SIGN MEDIAL LA
                                                if (0x1a56 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                                // Mc       TAI THAM CONSONANT SIGN LA TANG LAI
                                                if (0x1a57 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0x1a73) {
                                        if (code < 0x1a62) {
                                            if (code < 0x1a60) {
                                                // Mn   [7] TAI THAM SIGN MAI KANG LAI..TAI THAM CONSONANT SIGN SA
                                                if (0x1a58 <= code && code <= 0x1a5e) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn       TAI THAM SIGN SAKOT
                                                if (0x1a60 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1a65) {
                                                // Mn       TAI THAM VOWEL SIGN MAI SAT
                                                if (0x1a62 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x1a6d) {
                                                    // Mn   [8] TAI THAM VOWEL SIGN I..TAI THAM VOWEL SIGN OA BELOW
                                                    if (0x1a65 <= code && code <= 0x1a6c) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [6] TAI THAM VOWEL SIGN OY..TAI THAM VOWEL SIGN THAM AI
                                                    if (0x1a6d <= code && code <= 0x1a72) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x1b00) {
                                            if (code < 0x1a7f) {
                                                // Mn  [10] TAI THAM VOWEL SIGN OA ABOVE..TAI THAM SIGN KHUEN-LUE KARAN
                                                if (0x1a73 <= code && code <= 0x1a7c) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x1ab0) {
                                                    // Mn       TAI THAM COMBINING CRYPTOGRAMMIC DOT
                                                    if (0x1a7f === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn  [14] COMBINING DOUBLED CIRCUMFLEX ACCENT..COMBINING PARENTHESES BELOW
                                                    // Me       COMBINING PARENTHESES OVERLAY
                                                    // Mn  [16] COMBINING LATIN SMALL LETTER W BELOW..COMBINING LATIN SMALL LETTER INSULAR T
                                                    if (0x1ab0 <= code && code <= 0x1ace) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1b04) {
                                                // Mn   [4] BALINESE SIGN ULU RICEM..BALINESE SIGN SURANG
                                                if (0x1b00 <= code && code <= 0x1b03) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x1b34) {
                                                    // Mc       BALINESE SIGN BISAH
                                                    if (0x1b04 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn       BALINESE SIGN REREKAN
                                                    // Mc       BALINESE VOWEL SIGN TEDUNG
                                                    // Mn   [5] BALINESE VOWEL SIGN ULU..BALINESE VOWEL SIGN RA REPA
                                                    if (0x1b34 <= code && code <= 0x1b3a) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0x1ba8) {
                                    if (code < 0x1b6b) {
                                        if (code < 0x1b3d) {
                                            // Mc       BALINESE VOWEL SIGN RA REPA TEDUNG
                                            if (0x1b3b === code) {
                                                return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                            }
                                            // Mn       BALINESE VOWEL SIGN LA LENGA
                                            if (0x1b3c === code) {
                                                return boundaries_1.CLUSTER_BREAK.EXTEND;
                                            }
                                        }
                                        else {
                                            if (code < 0x1b42) {
                                                // Mc   [5] BALINESE VOWEL SIGN LA LENGA TEDUNG..BALINESE VOWEL SIGN TALING REPA TEDUNG
                                                if (0x1b3d <= code && code <= 0x1b41) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0x1b43) {
                                                    // Mn       BALINESE VOWEL SIGN PEPET
                                                    if (0x1b42 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] BALINESE VOWEL SIGN PEPET TEDUNG..BALINESE ADEG ADEG
                                                    if (0x1b43 <= code && code <= 0x1b44) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x1ba1) {
                                            if (code < 0x1b80) {
                                                // Mn   [9] BALINESE MUSICAL SYMBOL COMBINING TEGEH..BALINESE MUSICAL SYMBOL COMBINING GONG
                                                if (0x1b6b <= code && code <= 0x1b73) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x1b82) {
                                                    // Mn   [2] SUNDANESE SIGN PANYECEK..SUNDANESE SIGN PANGLAYAR
                                                    if (0x1b80 <= code && code <= 0x1b81) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc       SUNDANESE SIGN PANGWISAD
                                                    if (0x1b82 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1ba2) {
                                                // Mc       SUNDANESE CONSONANT SIGN PAMINGKAL
                                                if (0x1ba1 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0x1ba6) {
                                                    // Mn   [4] SUNDANESE CONSONANT SIGN PANYAKRA..SUNDANESE VOWEL SIGN PANYUKU
                                                    if (0x1ba2 <= code && code <= 0x1ba5) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] SUNDANESE VOWEL SIGN PANAELAENG..SUNDANESE VOWEL SIGN PANOLONG
                                                    if (0x1ba6 <= code && code <= 0x1ba7) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0x1be8) {
                                        if (code < 0x1bab) {
                                            if (code < 0x1baa) {
                                                // Mn   [2] SUNDANESE VOWEL SIGN PAMEPET..SUNDANESE VOWEL SIGN PANEULEUNG
                                                if (0x1ba8 <= code && code <= 0x1ba9) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mc       SUNDANESE SIGN PAMAAEH
                                                if (0x1baa === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1be6) {
                                                // Mn   [3] SUNDANESE SIGN VIRAMA..SUNDANESE CONSONANT SIGN PASANGAN WA
                                                if (0x1bab <= code && code <= 0x1bad) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn       BATAK SIGN TOMPI
                                                if (0x1be6 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                                // Mc       BATAK VOWEL SIGN E
                                                if (0x1be7 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x1bee) {
                                            if (code < 0x1bea) {
                                                // Mn   [2] BATAK VOWEL SIGN PAKPAK E..BATAK VOWEL SIGN EE
                                                if (0x1be8 <= code && code <= 0x1be9) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0x1bed) {
                                                    // Mc   [3] BATAK VOWEL SIGN I..BATAK VOWEL SIGN O
                                                    if (0x1bea <= code && code <= 0x1bec) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn       BATAK VOWEL SIGN KARO O
                                                    if (0x1bed === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1bef) {
                                                // Mc       BATAK VOWEL SIGN U
                                                if (0x1bee === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0x1bf2) {
                                                    // Mn   [3] BATAK VOWEL SIGN U FOR SIMALUNGUN SA..BATAK CONSONANT SIGN H
                                                    if (0x1bef <= code && code <= 0x1bf1) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] BATAK PANGOLAT..BATAK PANONGONAN
                                                    if (0x1bf2 <= code && code <= 0x1bf3) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else {
                        if (code < 0xa952) {
                            if (code < 0x2d7f) {
                                if (code < 0x1cf7) {
                                    if (code < 0x1cd4) {
                                        if (code < 0x1c34) {
                                            if (code < 0x1c2c) {
                                                // Mc   [8] LEPCHA SUBJOINED LETTER YA..LEPCHA VOWEL SIGN UU
                                                if (0x1c24 <= code && code <= 0x1c2b) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn   [8] LEPCHA VOWEL SIGN E..LEPCHA CONSONANT SIGN T
                                                if (0x1c2c <= code && code <= 0x1c33) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1c36) {
                                                // Mc   [2] LEPCHA CONSONANT SIGN NYIN-DO..LEPCHA CONSONANT SIGN KANG
                                                if (0x1c34 <= code && code <= 0x1c35) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0x1cd0) {
                                                    // Mn   [2] LEPCHA SIGN RAN..LEPCHA SIGN NUKTA
                                                    if (0x1c36 <= code && code <= 0x1c37) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [3] VEDIC TONE KARSHANA..VEDIC TONE PRENKHA
                                                    if (0x1cd0 <= code && code <= 0x1cd2) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x1ce2) {
                                            if (code < 0x1ce1) {
                                                // Mn  [13] VEDIC SIGN YAJURVEDIC MIDLINE SVARITA..VEDIC TONE RIGVEDIC KASHMIRI INDEPENDENT SVARITA
                                                if (0x1cd4 <= code && code <= 0x1ce0) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mc       VEDIC TONE ATHARVAVEDIC INDEPENDENT SVARITA
                                                if (0x1ce1 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x1ced) {
                                                // Mn   [7] VEDIC SIGN VISARGA SVARITA..VEDIC SIGN VISARGA ANUDATTA WITH TAIL
                                                if (0x1ce2 <= code && code <= 0x1ce8) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn       VEDIC SIGN TIRYAK
                                                if (0x1ced === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                                // Mn       VEDIC TONE CANDRA ABOVE
                                                if (0x1cf4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0x200d) {
                                        if (code < 0x1dc0) {
                                            if (code < 0x1cf8) {
                                                // Mc       VEDIC SIGN ATIKRAMA
                                                if (0x1cf7 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn   [2] VEDIC TONE RING ABOVE..VEDIC TONE DOUBLE RING ABOVE
                                                if (0x1cf8 <= code && code <= 0x1cf9) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x200b) {
                                                // Mn  [64] COMBINING DOTTED GRAVE ACCENT..COMBINING RIGHT ARROWHEAD AND DOWN ARROWHEAD BELOW
                                                if (0x1dc0 <= code && code <= 0x1dff) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Cf       ZERO WIDTH SPACE
                                                if (0x200b === code) {
                                                    return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                }
                                                // Cf       ZERO WIDTH NON-JOINER
                                                if (0x200c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0x2060) {
                                            if (code < 0x200e) {
                                                // Cf       ZERO WIDTH JOINER
                                                if (0x200d === code) {
                                                    return boundaries_1.CLUSTER_BREAK.ZWJ;
                                                }
                                            }
                                            else {
                                                if (code < 0x2028) {
                                                    // Cf   [2] LEFT-TO-RIGHT MARK..RIGHT-TO-LEFT MARK
                                                    if (0x200e <= code && code <= 0x200f) {
                                                        return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                    }
                                                }
                                                else {
                                                    // Zl       LINE SEPARATOR
                                                    // Zp       PARAGRAPH SEPARATOR
                                                    // Cf   [5] LEFT-TO-RIGHT EMBEDDING..RIGHT-TO-LEFT OVERRIDE
                                                    if (0x2028 <= code && code <= 0x202e) {
                                                        return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x20d0) {
                                                // Cf   [5] WORD JOINER..INVISIBLE PLUS
                                                // Cn       <reserved-2065>
                                                // Cf  [10] LEFT-TO-RIGHT ISOLATE..NOMINAL DIGIT SHAPES
                                                if (0x2060 <= code && code <= 0x206f) {
                                                    return boundaries_1.CLUSTER_BREAK.CONTROL;
                                                }
                                            }
                                            else {
                                                if (code < 0x2cef) {
                                                    // Mn  [13] COMBINING LEFT HARPOON ABOVE..COMBINING FOUR DOTS ABOVE
                                                    // Me   [4] COMBINING ENCLOSING CIRCLE..COMBINING ENCLOSING CIRCLE BACKSLASH
                                                    // Mn       COMBINING LEFT RIGHT ARROW ABOVE
                                                    // Me   [3] COMBINING ENCLOSING SCREEN..COMBINING ENCLOSING UPWARD POINTING TRIANGLE
                                                    // Mn  [12] COMBINING REVERSE SOLIDUS OVERLAY..COMBINING ASTERISK ABOVE
                                                    if (0x20d0 <= code && code <= 0x20f0) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [3] COPTIC COMBINING NI ABOVE..COPTIC COMBINING SPIRITUS LENIS
                                                    if (0x2cef <= code && code <= 0x2cf1) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xa823) {
                                    if (code < 0xa674) {
                                        if (code < 0x302a) {
                                            if (code < 0x2de0) {
                                                // Mn       TIFINAGH CONSONANT JOINER
                                                if (0x2d7f === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn  [32] COMBINING CYRILLIC LETTER BE..COMBINING CYRILLIC LETTER IOTIFIED BIG YUS
                                                if (0x2de0 <= code && code <= 0x2dff) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0x3099) {
                                                // Mn   [4] IDEOGRAPHIC LEVEL TONE MARK..IDEOGRAPHIC ENTERING TONE MARK
                                                // Mc   [2] HANGUL SINGLE DOT TONE MARK..HANGUL DOUBLE DOT TONE MARK
                                                if (0x302a <= code && code <= 0x302f) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xa66f) {
                                                    // Mn   [2] COMBINING KATAKANA-HIRAGANA VOICED SOUND MARK..COMBINING KATAKANA-HIRAGANA SEMI-VOICED SOUND MARK
                                                    if (0x3099 <= code && code <= 0x309a) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn       COMBINING CYRILLIC VZMET
                                                    // Me   [3] COMBINING CYRILLIC TEN MILLIONS SIGN..COMBINING CYRILLIC THOUSAND MILLIONS SIGN
                                                    if (0xa66f <= code && code <= 0xa672) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xa802) {
                                            if (code < 0xa69e) {
                                                // Mn  [10] COMBINING CYRILLIC LETTER UKRAINIAN IE..COMBINING CYRILLIC PAYEROK
                                                if (0xa674 <= code && code <= 0xa67d) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xa6f0) {
                                                    // Mn   [2] COMBINING CYRILLIC LETTER EF..COMBINING CYRILLIC LETTER IOTIFIED E
                                                    if (0xa69e <= code && code <= 0xa69f) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn   [2] BAMUM COMBINING MARK KOQNDON..BAMUM COMBINING MARK TUKWENTIS
                                                    if (0xa6f0 <= code && code <= 0xa6f1) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xa806) {
                                                // Mn       SYLOTI NAGRI SIGN DVISVARA
                                                if (0xa802 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn       SYLOTI NAGRI SIGN HASANTA
                                                if (0xa806 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                                // Mn       SYLOTI NAGRI SIGN ANUSVARA
                                                if (0xa80b === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xa8b4) {
                                        if (code < 0xa827) {
                                            if (code < 0xa825) {
                                                // Mc   [2] SYLOTI NAGRI VOWEL SIGN A..SYLOTI NAGRI VOWEL SIGN I
                                                if (0xa823 <= code && code <= 0xa824) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn   [2] SYLOTI NAGRI VOWEL SIGN U..SYLOTI NAGRI VOWEL SIGN E
                                                if (0xa825 <= code && code <= 0xa826) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xa82c) {
                                                // Mc       SYLOTI NAGRI VOWEL SIGN OO
                                                if (0xa827 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xa880) {
                                                    // Mn       SYLOTI NAGRI SIGN ALTERNATE HASANTA
                                                    if (0xa82c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] SAURASHTRA SIGN ANUSVARA..SAURASHTRA SIGN VISARGA
                                                    if (0xa880 <= code && code <= 0xa881) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xa8ff) {
                                            if (code < 0xa8c4) {
                                                // Mc  [16] SAURASHTRA CONSONANT SIGN HAARU..SAURASHTRA VOWEL SIGN AU
                                                if (0xa8b4 <= code && code <= 0xa8c3) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xa8e0) {
                                                    // Mn   [2] SAURASHTRA SIGN VIRAMA..SAURASHTRA SIGN CANDRABINDU
                                                    if (0xa8c4 <= code && code <= 0xa8c5) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn  [18] COMBINING DEVANAGARI DIGIT ZERO..COMBINING DEVANAGARI SIGN AVAGRAHA
                                                    if (0xa8e0 <= code && code <= 0xa8f1) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xa926) {
                                                // Mn       DEVANAGARI VOWEL SIGN AY
                                                if (0xa8ff === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xa947) {
                                                    // Mn   [8] KAYAH LI VOWEL UE..KAYAH LI TONE CALYA PLOPHU
                                                    if (0xa926 <= code && code <= 0xa92d) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mn  [11] REJANG VOWEL SIGN I..REJANG CONSONANT SIGN R
                                                    if (0xa947 <= code && code <= 0xa951) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            if (code < 0xaab2) {
                                if (code < 0xa9e5) {
                                    if (code < 0xa9b4) {
                                        if (code < 0xa980) {
                                            if (code < 0xa960) {
                                                // Mc   [2] REJANG CONSONANT SIGN H..REJANG VIRAMA
                                                if (0xa952 <= code && code <= 0xa953) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Lo  [29] HANGUL CHOSEONG TIKEUT-MIEUM..HANGUL CHOSEONG SSANGYEORINHIEUH
                                                if (0xa960 <= code && code <= 0xa97c) {
                                                    return boundaries_1.CLUSTER_BREAK.L;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xa983) {
                                                // Mn   [3] JAVANESE SIGN PANYANGGA..JAVANESE SIGN LAYAR
                                                if (0xa980 <= code && code <= 0xa982) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mc       JAVANESE SIGN WIGNYAN
                                                if (0xa983 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                                // Mn       JAVANESE SIGN CECAK TELU
                                                if (0xa9b3 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xa9ba) {
                                            if (code < 0xa9b6) {
                                                // Mc   [2] JAVANESE VOWEL SIGN TARUNG..JAVANESE VOWEL SIGN TOLONG
                                                if (0xa9b4 <= code && code <= 0xa9b5) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn   [4] JAVANESE VOWEL SIGN WULU..JAVANESE VOWEL SIGN SUKU MENDUT
                                                if (0xa9b6 <= code && code <= 0xa9b9) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xa9bc) {
                                                // Mc   [2] JAVANESE VOWEL SIGN TALING..JAVANESE VOWEL SIGN DIRGA MURE
                                                if (0xa9ba <= code && code <= 0xa9bb) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xa9be) {
                                                    // Mn   [2] JAVANESE VOWEL SIGN PEPET..JAVANESE CONSONANT SIGN KERET
                                                    if (0xa9bc <= code && code <= 0xa9bd) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [3] JAVANESE CONSONANT SIGN PENGKAL..JAVANESE PANGKON
                                                    if (0xa9be <= code && code <= 0xa9c0) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xaa35) {
                                        if (code < 0xaa2f) {
                                            if (code < 0xaa29) {
                                                // Mn       MYANMAR SIGN SHAN SAW
                                                if (0xa9e5 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn   [6] CHAM VOWEL SIGN AA..CHAM VOWEL SIGN OE
                                                if (0xaa29 <= code && code <= 0xaa2e) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xaa31) {
                                                // Mc   [2] CHAM VOWEL SIGN O..CHAM VOWEL SIGN AI
                                                if (0xaa2f <= code && code <= 0xaa30) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                if (code < 0xaa33) {
                                                    // Mn   [2] CHAM VOWEL SIGN AU..CHAM VOWEL SIGN UE
                                                    if (0xaa31 <= code && code <= 0xaa32) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                                else {
                                                    // Mc   [2] CHAM CONSONANT SIGN YA..CHAM CONSONANT SIGN RA
                                                    if (0xaa33 <= code && code <= 0xaa34) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xaa4d) {
                                            if (code < 0xaa43) {
                                                // Mn   [2] CHAM CONSONANT SIGN LA..CHAM CONSONANT SIGN WA
                                                if (0xaa35 <= code && code <= 0xaa36) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn       CHAM CONSONANT SIGN FINAL NG
                                                if (0xaa43 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                                // Mn       CHAM CONSONANT SIGN FINAL M
                                                if (0xaa4c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xaa7c) {
                                                // Mc       CHAM CONSONANT SIGN FINAL H
                                                if (0xaa4d === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn       MYANMAR SIGN TAI LAING TONE-2
                                                if (0xaa7c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                                // Mn       TAI VIET MAI KANG
                                                if (0xaab0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xabe6) {
                                    if (code < 0xaaec) {
                                        if (code < 0xaabe) {
                                            if (code < 0xaab7) {
                                                // Mn   [3] TAI VIET VOWEL I..TAI VIET VOWEL U
                                                if (0xaab2 <= code && code <= 0xaab4) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn   [2] TAI VIET MAI KHIT..TAI VIET VOWEL IA
                                                if (0xaab7 <= code && code <= 0xaab8) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xaac1) {
                                                // Mn   [2] TAI VIET VOWEL AM..TAI VIET TONE MAI EK
                                                if (0xaabe <= code && code <= 0xaabf) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                // Mn       TAI VIET TONE MAI THO
                                                if (0xaac1 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                                // Mc       MEETEI MAYEK VOWEL SIGN II
                                                if (0xaaeb === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xaaf6) {
                                            if (code < 0xaaee) {
                                                // Mn   [2] MEETEI MAYEK VOWEL SIGN UU..MEETEI MAYEK VOWEL SIGN AAI
                                                if (0xaaec <= code && code <= 0xaaed) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xaaf5) {
                                                    // Mc   [2] MEETEI MAYEK VOWEL SIGN AU..MEETEI MAYEK VOWEL SIGN AAU
                                                    if (0xaaee <= code && code <= 0xaaef) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mc       MEETEI MAYEK VOWEL SIGN VISARGA
                                                    if (0xaaf5 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xabe3) {
                                                // Mn       MEETEI MAYEK VIRAMA
                                                if (0xaaf6 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                            else {
                                                if (code < 0xabe5) {
                                                    // Mc   [2] MEETEI MAYEK VOWEL SIGN ONAP..MEETEI MAYEK VOWEL SIGN INAP
                                                    if (0xabe3 <= code && code <= 0xabe4) {
                                                        return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                    }
                                                }
                                                else {
                                                    // Mn       MEETEI MAYEK VOWEL SIGN ANAP
                                                    if (0xabe5 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xac00) {
                                        if (code < 0xabe9) {
                                            if (code < 0xabe8) {
                                                // Mc   [2] MEETEI MAYEK VOWEL SIGN YENAP..MEETEI MAYEK VOWEL SIGN SOUNAP
                                                if (0xabe6 <= code && code <= 0xabe7) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mn       MEETEI MAYEK VOWEL SIGN UNAP
                                                if (0xabe8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xabec) {
                                                // Mc   [2] MEETEI MAYEK VOWEL SIGN CHEINAP..MEETEI MAYEK VOWEL SIGN NUNG
                                                if (0xabe9 <= code && code <= 0xabea) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                            }
                                            else {
                                                // Mc       MEETEI MAYEK LUM IYEK
                                                if (0xabec === code) {
                                                    return boundaries_1.CLUSTER_BREAK.SPACINGMARK;
                                                }
                                                // Mn       MEETEI MAYEK APUN IYEK
                                                if (0xabed === code) {
                                                    return boundaries_1.CLUSTER_BREAK.EXTEND;
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xac1d) {
                                            if (code < 0xac01) {
                                                // Lo       HANGUL SYLLABLE GA
                                                if (0xac00 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xac1c) {
                                                    // Lo  [27] HANGUL SYLLABLE GAG..HANGUL SYLLABLE GAH
                                                    if (0xac01 <= code && code <= 0xac1b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE GAE
                                                    if (0xac1c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xac38) {
                                                // Lo  [27] HANGUL SYLLABLE GAEG..HANGUL SYLLABLE GAEH
                                                if (0xac1d <= code && code <= 0xac37) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xac39) {
                                                    // Lo       HANGUL SYLLABLE GYA
                                                    if (0xac38 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE GYAG..HANGUL SYLLABLE GYAH
                                                    if (0xac39 <= code && code <= 0xac53) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            else {
                if (code < 0xb5a1) {
                    if (code < 0xb0ed) {
                        if (code < 0xaea0) {
                            if (code < 0xad6d) {
                                if (code < 0xace0) {
                                    if (code < 0xac8d) {
                                        if (code < 0xac70) {
                                            if (code < 0xac55) {
                                                // Lo       HANGUL SYLLABLE GYAE
                                                if (0xac54 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE GYAEG..HANGUL SYLLABLE GYAEH
                                                if (0xac55 <= code && code <= 0xac6f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xac71) {
                                                // Lo       HANGUL SYLLABLE GEO
                                                if (0xac70 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xac8c) {
                                                    // Lo  [27] HANGUL SYLLABLE GEOG..HANGUL SYLLABLE GEOH
                                                    if (0xac71 <= code && code <= 0xac8b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE GE
                                                    if (0xac8c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xaca9) {
                                            if (code < 0xaca8) {
                                                // Lo  [27] HANGUL SYLLABLE GEG..HANGUL SYLLABLE GEH
                                                if (0xac8d <= code && code <= 0xaca7) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE GYEO
                                                if (0xaca8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xacc4) {
                                                // Lo  [27] HANGUL SYLLABLE GYEOG..HANGUL SYLLABLE GYEOH
                                                if (0xaca9 <= code && code <= 0xacc3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xacc5) {
                                                    // Lo       HANGUL SYLLABLE GYE
                                                    if (0xacc4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE GYEG..HANGUL SYLLABLE GYEH
                                                    if (0xacc5 <= code && code <= 0xacdf) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xad19) {
                                        if (code < 0xacfc) {
                                            if (code < 0xace1) {
                                                // Lo       HANGUL SYLLABLE GO
                                                if (0xace0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE GOG..HANGUL SYLLABLE GOH
                                                if (0xace1 <= code && code <= 0xacfb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xacfd) {
                                                // Lo       HANGUL SYLLABLE GWA
                                                if (0xacfc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xad18) {
                                                    // Lo  [27] HANGUL SYLLABLE GWAG..HANGUL SYLLABLE GWAH
                                                    if (0xacfd <= code && code <= 0xad17) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE GWAE
                                                    if (0xad18 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xad50) {
                                            if (code < 0xad34) {
                                                // Lo  [27] HANGUL SYLLABLE GWAEG..HANGUL SYLLABLE GWAEH
                                                if (0xad19 <= code && code <= 0xad33) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xad35) {
                                                    // Lo       HANGUL SYLLABLE GOE
                                                    if (0xad34 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE GOEG..HANGUL SYLLABLE GOEH
                                                    if (0xad35 <= code && code <= 0xad4f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xad51) {
                                                // Lo       HANGUL SYLLABLE GYO
                                                if (0xad50 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xad6c) {
                                                    // Lo  [27] HANGUL SYLLABLE GYOG..HANGUL SYLLABLE GYOH
                                                    if (0xad51 <= code && code <= 0xad6b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE GU
                                                    if (0xad6c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xadf9) {
                                    if (code < 0xadc0) {
                                        if (code < 0xad89) {
                                            if (code < 0xad88) {
                                                // Lo  [27] HANGUL SYLLABLE GUG..HANGUL SYLLABLE GUH
                                                if (0xad6d <= code && code <= 0xad87) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE GWEO
                                                if (0xad88 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xada4) {
                                                // Lo  [27] HANGUL SYLLABLE GWEOG..HANGUL SYLLABLE GWEOH
                                                if (0xad89 <= code && code <= 0xada3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xada5) {
                                                    // Lo       HANGUL SYLLABLE GWE
                                                    if (0xada4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE GWEG..HANGUL SYLLABLE GWEH
                                                    if (0xada5 <= code && code <= 0xadbf) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xaddc) {
                                            if (code < 0xadc1) {
                                                // Lo       HANGUL SYLLABLE GWI
                                                if (0xadc0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE GWIG..HANGUL SYLLABLE GWIH
                                                if (0xadc1 <= code && code <= 0xaddb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xaddd) {
                                                // Lo       HANGUL SYLLABLE GYU
                                                if (0xaddc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xadf8) {
                                                    // Lo  [27] HANGUL SYLLABLE GYUG..HANGUL SYLLABLE GYUH
                                                    if (0xaddd <= code && code <= 0xadf7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE GEU
                                                    if (0xadf8 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xae4c) {
                                        if (code < 0xae15) {
                                            if (code < 0xae14) {
                                                // Lo  [27] HANGUL SYLLABLE GEUG..HANGUL SYLLABLE GEUH
                                                if (0xadf9 <= code && code <= 0xae13) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE GYI
                                                if (0xae14 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xae30) {
                                                // Lo  [27] HANGUL SYLLABLE GYIG..HANGUL SYLLABLE GYIH
                                                if (0xae15 <= code && code <= 0xae2f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xae31) {
                                                    // Lo       HANGUL SYLLABLE GI
                                                    if (0xae30 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE GIG..HANGUL SYLLABLE GIH
                                                    if (0xae31 <= code && code <= 0xae4b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xae69) {
                                            if (code < 0xae4d) {
                                                // Lo       HANGUL SYLLABLE GGA
                                                if (0xae4c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xae68) {
                                                    // Lo  [27] HANGUL SYLLABLE GGAG..HANGUL SYLLABLE GGAH
                                                    if (0xae4d <= code && code <= 0xae67) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE GGAE
                                                    if (0xae68 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xae84) {
                                                // Lo  [27] HANGUL SYLLABLE GGAEG..HANGUL SYLLABLE GGAEH
                                                if (0xae69 <= code && code <= 0xae83) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xae85) {
                                                    // Lo       HANGUL SYLLABLE GGYA
                                                    if (0xae84 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE GGYAG..HANGUL SYLLABLE GGYAH
                                                    if (0xae85 <= code && code <= 0xae9f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            if (code < 0xafb9) {
                                if (code < 0xaf2c) {
                                    if (code < 0xaed9) {
                                        if (code < 0xaebc) {
                                            if (code < 0xaea1) {
                                                // Lo       HANGUL SYLLABLE GGYAE
                                                if (0xaea0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE GGYAEG..HANGUL SYLLABLE GGYAEH
                                                if (0xaea1 <= code && code <= 0xaebb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xaebd) {
                                                // Lo       HANGUL SYLLABLE GGEO
                                                if (0xaebc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xaed8) {
                                                    // Lo  [27] HANGUL SYLLABLE GGEOG..HANGUL SYLLABLE GGEOH
                                                    if (0xaebd <= code && code <= 0xaed7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE GGE
                                                    if (0xaed8 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xaef5) {
                                            if (code < 0xaef4) {
                                                // Lo  [27] HANGUL SYLLABLE GGEG..HANGUL SYLLABLE GGEH
                                                if (0xaed9 <= code && code <= 0xaef3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE GGYEO
                                                if (0xaef4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xaf10) {
                                                // Lo  [27] HANGUL SYLLABLE GGYEOG..HANGUL SYLLABLE GGYEOH
                                                if (0xaef5 <= code && code <= 0xaf0f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xaf11) {
                                                    // Lo       HANGUL SYLLABLE GGYE
                                                    if (0xaf10 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE GGYEG..HANGUL SYLLABLE GGYEH
                                                    if (0xaf11 <= code && code <= 0xaf2b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xaf65) {
                                        if (code < 0xaf48) {
                                            if (code < 0xaf2d) {
                                                // Lo       HANGUL SYLLABLE GGO
                                                if (0xaf2c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE GGOG..HANGUL SYLLABLE GGOH
                                                if (0xaf2d <= code && code <= 0xaf47) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xaf49) {
                                                // Lo       HANGUL SYLLABLE GGWA
                                                if (0xaf48 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xaf64) {
                                                    // Lo  [27] HANGUL SYLLABLE GGWAG..HANGUL SYLLABLE GGWAH
                                                    if (0xaf49 <= code && code <= 0xaf63) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE GGWAE
                                                    if (0xaf64 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xaf9c) {
                                            if (code < 0xaf80) {
                                                // Lo  [27] HANGUL SYLLABLE GGWAEG..HANGUL SYLLABLE GGWAEH
                                                if (0xaf65 <= code && code <= 0xaf7f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xaf81) {
                                                    // Lo       HANGUL SYLLABLE GGOE
                                                    if (0xaf80 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE GGOEG..HANGUL SYLLABLE GGOEH
                                                    if (0xaf81 <= code && code <= 0xaf9b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xaf9d) {
                                                // Lo       HANGUL SYLLABLE GGYO
                                                if (0xaf9c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xafb8) {
                                                    // Lo  [27] HANGUL SYLLABLE GGYOG..HANGUL SYLLABLE GGYOH
                                                    if (0xaf9d <= code && code <= 0xafb7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE GGU
                                                    if (0xafb8 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xb060) {
                                    if (code < 0xb00c) {
                                        if (code < 0xafd5) {
                                            if (code < 0xafd4) {
                                                // Lo  [27] HANGUL SYLLABLE GGUG..HANGUL SYLLABLE GGUH
                                                if (0xafb9 <= code && code <= 0xafd3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE GGWEO
                                                if (0xafd4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xaff0) {
                                                // Lo  [27] HANGUL SYLLABLE GGWEOG..HANGUL SYLLABLE GGWEOH
                                                if (0xafd5 <= code && code <= 0xafef) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xaff1) {
                                                    // Lo       HANGUL SYLLABLE GGWE
                                                    if (0xaff0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE GGWEG..HANGUL SYLLABLE GGWEH
                                                    if (0xaff1 <= code && code <= 0xb00b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb029) {
                                            if (code < 0xb00d) {
                                                // Lo       HANGUL SYLLABLE GGWI
                                                if (0xb00c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb028) {
                                                    // Lo  [27] HANGUL SYLLABLE GGWIG..HANGUL SYLLABLE GGWIH
                                                    if (0xb00d <= code && code <= 0xb027) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE GGYU
                                                    if (0xb028 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb044) {
                                                // Lo  [27] HANGUL SYLLABLE GGYUG..HANGUL SYLLABLE GGYUH
                                                if (0xb029 <= code && code <= 0xb043) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb045) {
                                                    // Lo       HANGUL SYLLABLE GGEU
                                                    if (0xb044 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE GGEUG..HANGUL SYLLABLE GGEUH
                                                    if (0xb045 <= code && code <= 0xb05f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xb099) {
                                        if (code < 0xb07c) {
                                            if (code < 0xb061) {
                                                // Lo       HANGUL SYLLABLE GGYI
                                                if (0xb060 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE GGYIG..HANGUL SYLLABLE GGYIH
                                                if (0xb061 <= code && code <= 0xb07b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb07d) {
                                                // Lo       HANGUL SYLLABLE GGI
                                                if (0xb07c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb098) {
                                                    // Lo  [27] HANGUL SYLLABLE GGIG..HANGUL SYLLABLE GGIH
                                                    if (0xb07d <= code && code <= 0xb097) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE NA
                                                    if (0xb098 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb0d0) {
                                            if (code < 0xb0b4) {
                                                // Lo  [27] HANGUL SYLLABLE NAG..HANGUL SYLLABLE NAH
                                                if (0xb099 <= code && code <= 0xb0b3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb0b5) {
                                                    // Lo       HANGUL SYLLABLE NAE
                                                    if (0xb0b4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE NAEG..HANGUL SYLLABLE NAEH
                                                    if (0xb0b5 <= code && code <= 0xb0cf) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb0d1) {
                                                // Lo       HANGUL SYLLABLE NYA
                                                if (0xb0d0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb0ec) {
                                                    // Lo  [27] HANGUL SYLLABLE NYAG..HANGUL SYLLABLE NYAH
                                                    if (0xb0d1 <= code && code <= 0xb0eb) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE NYAE
                                                    if (0xb0ec === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else {
                        if (code < 0xb354) {
                            if (code < 0xb220) {
                                if (code < 0xb179) {
                                    if (code < 0xb140) {
                                        if (code < 0xb109) {
                                            if (code < 0xb108) {
                                                // Lo  [27] HANGUL SYLLABLE NYAEG..HANGUL SYLLABLE NYAEH
                                                if (0xb0ed <= code && code <= 0xb107) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE NEO
                                                if (0xb108 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb124) {
                                                // Lo  [27] HANGUL SYLLABLE NEOG..HANGUL SYLLABLE NEOH
                                                if (0xb109 <= code && code <= 0xb123) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb125) {
                                                    // Lo       HANGUL SYLLABLE NE
                                                    if (0xb124 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE NEG..HANGUL SYLLABLE NEH
                                                    if (0xb125 <= code && code <= 0xb13f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb15c) {
                                            if (code < 0xb141) {
                                                // Lo       HANGUL SYLLABLE NYEO
                                                if (0xb140 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE NYEOG..HANGUL SYLLABLE NYEOH
                                                if (0xb141 <= code && code <= 0xb15b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb15d) {
                                                // Lo       HANGUL SYLLABLE NYE
                                                if (0xb15c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb178) {
                                                    // Lo  [27] HANGUL SYLLABLE NYEG..HANGUL SYLLABLE NYEH
                                                    if (0xb15d <= code && code <= 0xb177) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE NO
                                                    if (0xb178 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xb1cc) {
                                        if (code < 0xb195) {
                                            if (code < 0xb194) {
                                                // Lo  [27] HANGUL SYLLABLE NOG..HANGUL SYLLABLE NOH
                                                if (0xb179 <= code && code <= 0xb193) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE NWA
                                                if (0xb194 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb1b0) {
                                                // Lo  [27] HANGUL SYLLABLE NWAG..HANGUL SYLLABLE NWAH
                                                if (0xb195 <= code && code <= 0xb1af) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb1b1) {
                                                    // Lo       HANGUL SYLLABLE NWAE
                                                    if (0xb1b0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE NWAEG..HANGUL SYLLABLE NWAEH
                                                    if (0xb1b1 <= code && code <= 0xb1cb) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb1e9) {
                                            if (code < 0xb1cd) {
                                                // Lo       HANGUL SYLLABLE NOE
                                                if (0xb1cc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb1e8) {
                                                    // Lo  [27] HANGUL SYLLABLE NOEG..HANGUL SYLLABLE NOEH
                                                    if (0xb1cd <= code && code <= 0xb1e7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE NYO
                                                    if (0xb1e8 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb204) {
                                                // Lo  [27] HANGUL SYLLABLE NYOG..HANGUL SYLLABLE NYOH
                                                if (0xb1e9 <= code && code <= 0xb203) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb205) {
                                                    // Lo       HANGUL SYLLABLE NU
                                                    if (0xb204 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE NUG..HANGUL SYLLABLE NUH
                                                    if (0xb205 <= code && code <= 0xb21f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xb2ad) {
                                    if (code < 0xb259) {
                                        if (code < 0xb23c) {
                                            if (code < 0xb221) {
                                                // Lo       HANGUL SYLLABLE NWEO
                                                if (0xb220 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE NWEOG..HANGUL SYLLABLE NWEOH
                                                if (0xb221 <= code && code <= 0xb23b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb23d) {
                                                // Lo       HANGUL SYLLABLE NWE
                                                if (0xb23c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb258) {
                                                    // Lo  [27] HANGUL SYLLABLE NWEG..HANGUL SYLLABLE NWEH
                                                    if (0xb23d <= code && code <= 0xb257) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE NWI
                                                    if (0xb258 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb290) {
                                            if (code < 0xb274) {
                                                // Lo  [27] HANGUL SYLLABLE NWIG..HANGUL SYLLABLE NWIH
                                                if (0xb259 <= code && code <= 0xb273) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb275) {
                                                    // Lo       HANGUL SYLLABLE NYU
                                                    if (0xb274 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE NYUG..HANGUL SYLLABLE NYUH
                                                    if (0xb275 <= code && code <= 0xb28f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb291) {
                                                // Lo       HANGUL SYLLABLE NEU
                                                if (0xb290 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb2ac) {
                                                    // Lo  [27] HANGUL SYLLABLE NEUG..HANGUL SYLLABLE NEUH
                                                    if (0xb291 <= code && code <= 0xb2ab) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE NYI
                                                    if (0xb2ac === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xb300) {
                                        if (code < 0xb2c9) {
                                            if (code < 0xb2c8) {
                                                // Lo  [27] HANGUL SYLLABLE NYIG..HANGUL SYLLABLE NYIH
                                                if (0xb2ad <= code && code <= 0xb2c7) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE NI
                                                if (0xb2c8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb2e4) {
                                                // Lo  [27] HANGUL SYLLABLE NIG..HANGUL SYLLABLE NIH
                                                if (0xb2c9 <= code && code <= 0xb2e3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb2e5) {
                                                    // Lo       HANGUL SYLLABLE DA
                                                    if (0xb2e4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DAG..HANGUL SYLLABLE DAH
                                                    if (0xb2e5 <= code && code <= 0xb2ff) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb31d) {
                                            if (code < 0xb301) {
                                                // Lo       HANGUL SYLLABLE DAE
                                                if (0xb300 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb31c) {
                                                    // Lo  [27] HANGUL SYLLABLE DAEG..HANGUL SYLLABLE DAEH
                                                    if (0xb301 <= code && code <= 0xb31b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DYA
                                                    if (0xb31c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb338) {
                                                // Lo  [27] HANGUL SYLLABLE DYAG..HANGUL SYLLABLE DYAH
                                                if (0xb31d <= code && code <= 0xb337) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb339) {
                                                    // Lo       HANGUL SYLLABLE DYAE
                                                    if (0xb338 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DYAEG..HANGUL SYLLABLE DYAEH
                                                    if (0xb339 <= code && code <= 0xb353) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            if (code < 0xb46d) {
                                if (code < 0xb3e0) {
                                    if (code < 0xb38d) {
                                        if (code < 0xb370) {
                                            if (code < 0xb355) {
                                                // Lo       HANGUL SYLLABLE DEO
                                                if (0xb354 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE DEOG..HANGUL SYLLABLE DEOH
                                                if (0xb355 <= code && code <= 0xb36f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb371) {
                                                // Lo       HANGUL SYLLABLE DE
                                                if (0xb370 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb38c) {
                                                    // Lo  [27] HANGUL SYLLABLE DEG..HANGUL SYLLABLE DEH
                                                    if (0xb371 <= code && code <= 0xb38b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DYEO
                                                    if (0xb38c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb3a9) {
                                            if (code < 0xb3a8) {
                                                // Lo  [27] HANGUL SYLLABLE DYEOG..HANGUL SYLLABLE DYEOH
                                                if (0xb38d <= code && code <= 0xb3a7) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE DYE
                                                if (0xb3a8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb3c4) {
                                                // Lo  [27] HANGUL SYLLABLE DYEG..HANGUL SYLLABLE DYEH
                                                if (0xb3a9 <= code && code <= 0xb3c3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb3c5) {
                                                    // Lo       HANGUL SYLLABLE DO
                                                    if (0xb3c4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DOG..HANGUL SYLLABLE DOH
                                                    if (0xb3c5 <= code && code <= 0xb3df) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xb419) {
                                        if (code < 0xb3fc) {
                                            if (code < 0xb3e1) {
                                                // Lo       HANGUL SYLLABLE DWA
                                                if (0xb3e0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE DWAG..HANGUL SYLLABLE DWAH
                                                if (0xb3e1 <= code && code <= 0xb3fb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb3fd) {
                                                // Lo       HANGUL SYLLABLE DWAE
                                                if (0xb3fc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb418) {
                                                    // Lo  [27] HANGUL SYLLABLE DWAEG..HANGUL SYLLABLE DWAEH
                                                    if (0xb3fd <= code && code <= 0xb417) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DOE
                                                    if (0xb418 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb450) {
                                            if (code < 0xb434) {
                                                // Lo  [27] HANGUL SYLLABLE DOEG..HANGUL SYLLABLE DOEH
                                                if (0xb419 <= code && code <= 0xb433) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb435) {
                                                    // Lo       HANGUL SYLLABLE DYO
                                                    if (0xb434 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DYOG..HANGUL SYLLABLE DYOH
                                                    if (0xb435 <= code && code <= 0xb44f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb451) {
                                                // Lo       HANGUL SYLLABLE DU
                                                if (0xb450 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb46c) {
                                                    // Lo  [27] HANGUL SYLLABLE DUG..HANGUL SYLLABLE DUH
                                                    if (0xb451 <= code && code <= 0xb46b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DWEO
                                                    if (0xb46c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xb514) {
                                    if (code < 0xb4c0) {
                                        if (code < 0xb489) {
                                            if (code < 0xb488) {
                                                // Lo  [27] HANGUL SYLLABLE DWEOG..HANGUL SYLLABLE DWEOH
                                                if (0xb46d <= code && code <= 0xb487) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE DWE
                                                if (0xb488 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb4a4) {
                                                // Lo  [27] HANGUL SYLLABLE DWEG..HANGUL SYLLABLE DWEH
                                                if (0xb489 <= code && code <= 0xb4a3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb4a5) {
                                                    // Lo       HANGUL SYLLABLE DWI
                                                    if (0xb4a4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DWIG..HANGUL SYLLABLE DWIH
                                                    if (0xb4a5 <= code && code <= 0xb4bf) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb4dd) {
                                            if (code < 0xb4c1) {
                                                // Lo       HANGUL SYLLABLE DYU
                                                if (0xb4c0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb4dc) {
                                                    // Lo  [27] HANGUL SYLLABLE DYUG..HANGUL SYLLABLE DYUH
                                                    if (0xb4c1 <= code && code <= 0xb4db) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DEU
                                                    if (0xb4dc === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb4f8) {
                                                // Lo  [27] HANGUL SYLLABLE DEUG..HANGUL SYLLABLE DEUH
                                                if (0xb4dd <= code && code <= 0xb4f7) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb4f9) {
                                                    // Lo       HANGUL SYLLABLE DYI
                                                    if (0xb4f8 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DYIG..HANGUL SYLLABLE DYIH
                                                    if (0xb4f9 <= code && code <= 0xb513) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xb54d) {
                                        if (code < 0xb530) {
                                            if (code < 0xb515) {
                                                // Lo       HANGUL SYLLABLE DI
                                                if (0xb514 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE DIG..HANGUL SYLLABLE DIH
                                                if (0xb515 <= code && code <= 0xb52f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb531) {
                                                // Lo       HANGUL SYLLABLE DDA
                                                if (0xb530 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb54c) {
                                                    // Lo  [27] HANGUL SYLLABLE DDAG..HANGUL SYLLABLE DDAH
                                                    if (0xb531 <= code && code <= 0xb54b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DDAE
                                                    if (0xb54c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb584) {
                                            if (code < 0xb568) {
                                                // Lo  [27] HANGUL SYLLABLE DDAEG..HANGUL SYLLABLE DDAEH
                                                if (0xb54d <= code && code <= 0xb567) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb569) {
                                                    // Lo       HANGUL SYLLABLE DDYA
                                                    if (0xb568 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DDYAG..HANGUL SYLLABLE DDYAH
                                                    if (0xb569 <= code && code <= 0xb583) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb585) {
                                                // Lo       HANGUL SYLLABLE DDYAE
                                                if (0xb584 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb5a0) {
                                                    // Lo  [27] HANGUL SYLLABLE DDYAEG..HANGUL SYLLABLE DDYAEH
                                                    if (0xb585 <= code && code <= 0xb59f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DDEO
                                                    if (0xb5a0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else {
                    if (code < 0xba55) {
                        if (code < 0xb808) {
                            if (code < 0xb6d4) {
                                if (code < 0xb62d) {
                                    if (code < 0xb5f4) {
                                        if (code < 0xb5bd) {
                                            if (code < 0xb5bc) {
                                                // Lo  [27] HANGUL SYLLABLE DDEOG..HANGUL SYLLABLE DDEOH
                                                if (0xb5a1 <= code && code <= 0xb5bb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE DDE
                                                if (0xb5bc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb5d8) {
                                                // Lo  [27] HANGUL SYLLABLE DDEG..HANGUL SYLLABLE DDEH
                                                if (0xb5bd <= code && code <= 0xb5d7) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb5d9) {
                                                    // Lo       HANGUL SYLLABLE DDYEO
                                                    if (0xb5d8 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DDYEOG..HANGUL SYLLABLE DDYEOH
                                                    if (0xb5d9 <= code && code <= 0xb5f3) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb610) {
                                            if (code < 0xb5f5) {
                                                // Lo       HANGUL SYLLABLE DDYE
                                                if (0xb5f4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE DDYEG..HANGUL SYLLABLE DDYEH
                                                if (0xb5f5 <= code && code <= 0xb60f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb611) {
                                                // Lo       HANGUL SYLLABLE DDO
                                                if (0xb610 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb62c) {
                                                    // Lo  [27] HANGUL SYLLABLE DDOG..HANGUL SYLLABLE DDOH
                                                    if (0xb611 <= code && code <= 0xb62b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DDWA
                                                    if (0xb62c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xb680) {
                                        if (code < 0xb649) {
                                            if (code < 0xb648) {
                                                // Lo  [27] HANGUL SYLLABLE DDWAG..HANGUL SYLLABLE DDWAH
                                                if (0xb62d <= code && code <= 0xb647) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE DDWAE
                                                if (0xb648 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb664) {
                                                // Lo  [27] HANGUL SYLLABLE DDWAEG..HANGUL SYLLABLE DDWAEH
                                                if (0xb649 <= code && code <= 0xb663) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb665) {
                                                    // Lo       HANGUL SYLLABLE DDOE
                                                    if (0xb664 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DDOEG..HANGUL SYLLABLE DDOEH
                                                    if (0xb665 <= code && code <= 0xb67f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb69d) {
                                            if (code < 0xb681) {
                                                // Lo       HANGUL SYLLABLE DDYO
                                                if (0xb680 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb69c) {
                                                    // Lo  [27] HANGUL SYLLABLE DDYOG..HANGUL SYLLABLE DDYOH
                                                    if (0xb681 <= code && code <= 0xb69b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DDU
                                                    if (0xb69c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb6b8) {
                                                // Lo  [27] HANGUL SYLLABLE DDUG..HANGUL SYLLABLE DDUH
                                                if (0xb69d <= code && code <= 0xb6b7) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb6b9) {
                                                    // Lo       HANGUL SYLLABLE DDWEO
                                                    if (0xb6b8 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DDWEOG..HANGUL SYLLABLE DDWEOH
                                                    if (0xb6b9 <= code && code <= 0xb6d3) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xb761) {
                                    if (code < 0xb70d) {
                                        if (code < 0xb6f0) {
                                            if (code < 0xb6d5) {
                                                // Lo       HANGUL SYLLABLE DDWE
                                                if (0xb6d4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE DDWEG..HANGUL SYLLABLE DDWEH
                                                if (0xb6d5 <= code && code <= 0xb6ef) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb6f1) {
                                                // Lo       HANGUL SYLLABLE DDWI
                                                if (0xb6f0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb70c) {
                                                    // Lo  [27] HANGUL SYLLABLE DDWIG..HANGUL SYLLABLE DDWIH
                                                    if (0xb6f1 <= code && code <= 0xb70b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DDYU
                                                    if (0xb70c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb744) {
                                            if (code < 0xb728) {
                                                // Lo  [27] HANGUL SYLLABLE DDYUG..HANGUL SYLLABLE DDYUH
                                                if (0xb70d <= code && code <= 0xb727) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb729) {
                                                    // Lo       HANGUL SYLLABLE DDEU
                                                    if (0xb728 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE DDEUG..HANGUL SYLLABLE DDEUH
                                                    if (0xb729 <= code && code <= 0xb743) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb745) {
                                                // Lo       HANGUL SYLLABLE DDYI
                                                if (0xb744 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb760) {
                                                    // Lo  [27] HANGUL SYLLABLE DDYIG..HANGUL SYLLABLE DDYIH
                                                    if (0xb745 <= code && code <= 0xb75f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE DDI
                                                    if (0xb760 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xb7b4) {
                                        if (code < 0xb77d) {
                                            if (code < 0xb77c) {
                                                // Lo  [27] HANGUL SYLLABLE DDIG..HANGUL SYLLABLE DDIH
                                                if (0xb761 <= code && code <= 0xb77b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE RA
                                                if (0xb77c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb798) {
                                                // Lo  [27] HANGUL SYLLABLE RAG..HANGUL SYLLABLE RAH
                                                if (0xb77d <= code && code <= 0xb797) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb799) {
                                                    // Lo       HANGUL SYLLABLE RAE
                                                    if (0xb798 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE RAEG..HANGUL SYLLABLE RAEH
                                                    if (0xb799 <= code && code <= 0xb7b3) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb7d1) {
                                            if (code < 0xb7b5) {
                                                // Lo       HANGUL SYLLABLE RYA
                                                if (0xb7b4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb7d0) {
                                                    // Lo  [27] HANGUL SYLLABLE RYAG..HANGUL SYLLABLE RYAH
                                                    if (0xb7b5 <= code && code <= 0xb7cf) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE RYAE
                                                    if (0xb7d0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb7ec) {
                                                // Lo  [27] HANGUL SYLLABLE RYAEG..HANGUL SYLLABLE RYAEH
                                                if (0xb7d1 <= code && code <= 0xb7eb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb7ed) {
                                                    // Lo       HANGUL SYLLABLE REO
                                                    if (0xb7ec === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE REOG..HANGUL SYLLABLE REOH
                                                    if (0xb7ed <= code && code <= 0xb807) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            if (code < 0xb921) {
                                if (code < 0xb894) {
                                    if (code < 0xb841) {
                                        if (code < 0xb824) {
                                            if (code < 0xb809) {
                                                // Lo       HANGUL SYLLABLE RE
                                                if (0xb808 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE REG..HANGUL SYLLABLE REH
                                                if (0xb809 <= code && code <= 0xb823) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb825) {
                                                // Lo       HANGUL SYLLABLE RYEO
                                                if (0xb824 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb840) {
                                                    // Lo  [27] HANGUL SYLLABLE RYEOG..HANGUL SYLLABLE RYEOH
                                                    if (0xb825 <= code && code <= 0xb83f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE RYE
                                                    if (0xb840 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb85d) {
                                            if (code < 0xb85c) {
                                                // Lo  [27] HANGUL SYLLABLE RYEG..HANGUL SYLLABLE RYEH
                                                if (0xb841 <= code && code <= 0xb85b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE RO
                                                if (0xb85c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb878) {
                                                // Lo  [27] HANGUL SYLLABLE ROG..HANGUL SYLLABLE ROH
                                                if (0xb85d <= code && code <= 0xb877) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb879) {
                                                    // Lo       HANGUL SYLLABLE RWA
                                                    if (0xb878 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE RWAG..HANGUL SYLLABLE RWAH
                                                    if (0xb879 <= code && code <= 0xb893) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xb8cd) {
                                        if (code < 0xb8b0) {
                                            if (code < 0xb895) {
                                                // Lo       HANGUL SYLLABLE RWAE
                                                if (0xb894 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE RWAEG..HANGUL SYLLABLE RWAEH
                                                if (0xb895 <= code && code <= 0xb8af) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb8b1) {
                                                // Lo       HANGUL SYLLABLE ROE
                                                if (0xb8b0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb8cc) {
                                                    // Lo  [27] HANGUL SYLLABLE ROEG..HANGUL SYLLABLE ROEH
                                                    if (0xb8b1 <= code && code <= 0xb8cb) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE RYO
                                                    if (0xb8cc === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb904) {
                                            if (code < 0xb8e8) {
                                                // Lo  [27] HANGUL SYLLABLE RYOG..HANGUL SYLLABLE RYOH
                                                if (0xb8cd <= code && code <= 0xb8e7) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb8e9) {
                                                    // Lo       HANGUL SYLLABLE RU
                                                    if (0xb8e8 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE RUG..HANGUL SYLLABLE RUH
                                                    if (0xb8e9 <= code && code <= 0xb903) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb905) {
                                                // Lo       HANGUL SYLLABLE RWEO
                                                if (0xb904 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb920) {
                                                    // Lo  [27] HANGUL SYLLABLE RWEOG..HANGUL SYLLABLE RWEOH
                                                    if (0xb905 <= code && code <= 0xb91f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE RWE
                                                    if (0xb920 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xb9c8) {
                                    if (code < 0xb974) {
                                        if (code < 0xb93d) {
                                            if (code < 0xb93c) {
                                                // Lo  [27] HANGUL SYLLABLE RWEG..HANGUL SYLLABLE RWEH
                                                if (0xb921 <= code && code <= 0xb93b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE RWI
                                                if (0xb93c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb958) {
                                                // Lo  [27] HANGUL SYLLABLE RWIG..HANGUL SYLLABLE RWIH
                                                if (0xb93d <= code && code <= 0xb957) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb959) {
                                                    // Lo       HANGUL SYLLABLE RYU
                                                    if (0xb958 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE RYUG..HANGUL SYLLABLE RYUH
                                                    if (0xb959 <= code && code <= 0xb973) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xb991) {
                                            if (code < 0xb975) {
                                                // Lo       HANGUL SYLLABLE REU
                                                if (0xb974 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xb990) {
                                                    // Lo  [27] HANGUL SYLLABLE REUG..HANGUL SYLLABLE REUH
                                                    if (0xb975 <= code && code <= 0xb98f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE RYI
                                                    if (0xb990 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb9ac) {
                                                // Lo  [27] HANGUL SYLLABLE RYIG..HANGUL SYLLABLE RYIH
                                                if (0xb991 <= code && code <= 0xb9ab) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xb9ad) {
                                                    // Lo       HANGUL SYLLABLE RI
                                                    if (0xb9ac === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE RIG..HANGUL SYLLABLE RIH
                                                    if (0xb9ad <= code && code <= 0xb9c7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xba01) {
                                        if (code < 0xb9e4) {
                                            if (code < 0xb9c9) {
                                                // Lo       HANGUL SYLLABLE MA
                                                if (0xb9c8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE MAG..HANGUL SYLLABLE MAH
                                                if (0xb9c9 <= code && code <= 0xb9e3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xb9e5) {
                                                // Lo       HANGUL SYLLABLE MAE
                                                if (0xb9e4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xba00) {
                                                    // Lo  [27] HANGUL SYLLABLE MAEG..HANGUL SYLLABLE MAEH
                                                    if (0xb9e5 <= code && code <= 0xb9ff) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE MYA
                                                    if (0xba00 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xba38) {
                                            if (code < 0xba1c) {
                                                // Lo  [27] HANGUL SYLLABLE MYAG..HANGUL SYLLABLE MYAH
                                                if (0xba01 <= code && code <= 0xba1b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xba1d) {
                                                    // Lo       HANGUL SYLLABLE MYAE
                                                    if (0xba1c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE MYAEG..HANGUL SYLLABLE MYAEH
                                                    if (0xba1d <= code && code <= 0xba37) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xba39) {
                                                // Lo       HANGUL SYLLABLE MEO
                                                if (0xba38 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xba54) {
                                                    // Lo  [27] HANGUL SYLLABLE MEOG..HANGUL SYLLABLE MEOH
                                                    if (0xba39 <= code && code <= 0xba53) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE ME
                                                    if (0xba54 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else {
                        if (code < 0xbcbc) {
                            if (code < 0xbb88) {
                                if (code < 0xbae1) {
                                    if (code < 0xbaa8) {
                                        if (code < 0xba71) {
                                            if (code < 0xba70) {
                                                // Lo  [27] HANGUL SYLLABLE MEG..HANGUL SYLLABLE MEH
                                                if (0xba55 <= code && code <= 0xba6f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE MYEO
                                                if (0xba70 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xba8c) {
                                                // Lo  [27] HANGUL SYLLABLE MYEOG..HANGUL SYLLABLE MYEOH
                                                if (0xba71 <= code && code <= 0xba8b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xba8d) {
                                                    // Lo       HANGUL SYLLABLE MYE
                                                    if (0xba8c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE MYEG..HANGUL SYLLABLE MYEH
                                                    if (0xba8d <= code && code <= 0xbaa7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xbac4) {
                                            if (code < 0xbaa9) {
                                                // Lo       HANGUL SYLLABLE MO
                                                if (0xbaa8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE MOG..HANGUL SYLLABLE MOH
                                                if (0xbaa9 <= code && code <= 0xbac3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbac5) {
                                                // Lo       HANGUL SYLLABLE MWA
                                                if (0xbac4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbae0) {
                                                    // Lo  [27] HANGUL SYLLABLE MWAG..HANGUL SYLLABLE MWAH
                                                    if (0xbac5 <= code && code <= 0xbadf) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE MWAE
                                                    if (0xbae0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xbb34) {
                                        if (code < 0xbafd) {
                                            if (code < 0xbafc) {
                                                // Lo  [27] HANGUL SYLLABLE MWAEG..HANGUL SYLLABLE MWAEH
                                                if (0xbae1 <= code && code <= 0xbafb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE MOE
                                                if (0xbafc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbb18) {
                                                // Lo  [27] HANGUL SYLLABLE MOEG..HANGUL SYLLABLE MOEH
                                                if (0xbafd <= code && code <= 0xbb17) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbb19) {
                                                    // Lo       HANGUL SYLLABLE MYO
                                                    if (0xbb18 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE MYOG..HANGUL SYLLABLE MYOH
                                                    if (0xbb19 <= code && code <= 0xbb33) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xbb51) {
                                            if (code < 0xbb35) {
                                                // Lo       HANGUL SYLLABLE MU
                                                if (0xbb34 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbb50) {
                                                    // Lo  [27] HANGUL SYLLABLE MUG..HANGUL SYLLABLE MUH
                                                    if (0xbb35 <= code && code <= 0xbb4f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE MWEO
                                                    if (0xbb50 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbb6c) {
                                                // Lo  [27] HANGUL SYLLABLE MWEOG..HANGUL SYLLABLE MWEOH
                                                if (0xbb51 <= code && code <= 0xbb6b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbb6d) {
                                                    // Lo       HANGUL SYLLABLE MWE
                                                    if (0xbb6c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE MWEG..HANGUL SYLLABLE MWEH
                                                    if (0xbb6d <= code && code <= 0xbb87) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xbc15) {
                                    if (code < 0xbbc1) {
                                        if (code < 0xbba4) {
                                            if (code < 0xbb89) {
                                                // Lo       HANGUL SYLLABLE MWI
                                                if (0xbb88 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE MWIG..HANGUL SYLLABLE MWIH
                                                if (0xbb89 <= code && code <= 0xbba3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbba5) {
                                                // Lo       HANGUL SYLLABLE MYU
                                                if (0xbba4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbbc0) {
                                                    // Lo  [27] HANGUL SYLLABLE MYUG..HANGUL SYLLABLE MYUH
                                                    if (0xbba5 <= code && code <= 0xbbbf) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE MEU
                                                    if (0xbbc0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xbbf8) {
                                            if (code < 0xbbdc) {
                                                // Lo  [27] HANGUL SYLLABLE MEUG..HANGUL SYLLABLE MEUH
                                                if (0xbbc1 <= code && code <= 0xbbdb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbbdd) {
                                                    // Lo       HANGUL SYLLABLE MYI
                                                    if (0xbbdc === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE MYIG..HANGUL SYLLABLE MYIH
                                                    if (0xbbdd <= code && code <= 0xbbf7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbbf9) {
                                                // Lo       HANGUL SYLLABLE MI
                                                if (0xbbf8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbc14) {
                                                    // Lo  [27] HANGUL SYLLABLE MIG..HANGUL SYLLABLE MIH
                                                    if (0xbbf9 <= code && code <= 0xbc13) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BA
                                                    if (0xbc14 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xbc68) {
                                        if (code < 0xbc31) {
                                            if (code < 0xbc30) {
                                                // Lo  [27] HANGUL SYLLABLE BAG..HANGUL SYLLABLE BAH
                                                if (0xbc15 <= code && code <= 0xbc2f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE BAE
                                                if (0xbc30 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbc4c) {
                                                // Lo  [27] HANGUL SYLLABLE BAEG..HANGUL SYLLABLE BAEH
                                                if (0xbc31 <= code && code <= 0xbc4b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbc4d) {
                                                    // Lo       HANGUL SYLLABLE BYA
                                                    if (0xbc4c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE BYAG..HANGUL SYLLABLE BYAH
                                                    if (0xbc4d <= code && code <= 0xbc67) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xbc85) {
                                            if (code < 0xbc69) {
                                                // Lo       HANGUL SYLLABLE BYAE
                                                if (0xbc68 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbc84) {
                                                    // Lo  [27] HANGUL SYLLABLE BYAEG..HANGUL SYLLABLE BYAEH
                                                    if (0xbc69 <= code && code <= 0xbc83) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BEO
                                                    if (0xbc84 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbca0) {
                                                // Lo  [27] HANGUL SYLLABLE BEOG..HANGUL SYLLABLE BEOH
                                                if (0xbc85 <= code && code <= 0xbc9f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbca1) {
                                                    // Lo       HANGUL SYLLABLE BE
                                                    if (0xbca0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE BEG..HANGUL SYLLABLE BEH
                                                    if (0xbca1 <= code && code <= 0xbcbb) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            if (code < 0xbdd5) {
                                if (code < 0xbd48) {
                                    if (code < 0xbcf5) {
                                        if (code < 0xbcd8) {
                                            if (code < 0xbcbd) {
                                                // Lo       HANGUL SYLLABLE BYEO
                                                if (0xbcbc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE BYEOG..HANGUL SYLLABLE BYEOH
                                                if (0xbcbd <= code && code <= 0xbcd7) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbcd9) {
                                                // Lo       HANGUL SYLLABLE BYE
                                                if (0xbcd8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbcf4) {
                                                    // Lo  [27] HANGUL SYLLABLE BYEG..HANGUL SYLLABLE BYEH
                                                    if (0xbcd9 <= code && code <= 0xbcf3) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BO
                                                    if (0xbcf4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xbd11) {
                                            if (code < 0xbd10) {
                                                // Lo  [27] HANGUL SYLLABLE BOG..HANGUL SYLLABLE BOH
                                                if (0xbcf5 <= code && code <= 0xbd0f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE BWA
                                                if (0xbd10 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbd2c) {
                                                // Lo  [27] HANGUL SYLLABLE BWAG..HANGUL SYLLABLE BWAH
                                                if (0xbd11 <= code && code <= 0xbd2b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbd2d) {
                                                    // Lo       HANGUL SYLLABLE BWAE
                                                    if (0xbd2c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE BWAEG..HANGUL SYLLABLE BWAEH
                                                    if (0xbd2d <= code && code <= 0xbd47) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xbd81) {
                                        if (code < 0xbd64) {
                                            if (code < 0xbd49) {
                                                // Lo       HANGUL SYLLABLE BOE
                                                if (0xbd48 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE BOEG..HANGUL SYLLABLE BOEH
                                                if (0xbd49 <= code && code <= 0xbd63) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbd65) {
                                                // Lo       HANGUL SYLLABLE BYO
                                                if (0xbd64 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbd80) {
                                                    // Lo  [27] HANGUL SYLLABLE BYOG..HANGUL SYLLABLE BYOH
                                                    if (0xbd65 <= code && code <= 0xbd7f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BU
                                                    if (0xbd80 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xbdb8) {
                                            if (code < 0xbd9c) {
                                                // Lo  [27] HANGUL SYLLABLE BUG..HANGUL SYLLABLE BUH
                                                if (0xbd81 <= code && code <= 0xbd9b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbd9d) {
                                                    // Lo       HANGUL SYLLABLE BWEO
                                                    if (0xbd9c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE BWEOG..HANGUL SYLLABLE BWEOH
                                                    if (0xbd9d <= code && code <= 0xbdb7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbdb9) {
                                                // Lo       HANGUL SYLLABLE BWE
                                                if (0xbdb8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbdd4) {
                                                    // Lo  [27] HANGUL SYLLABLE BWEG..HANGUL SYLLABLE BWEH
                                                    if (0xbdb9 <= code && code <= 0xbdd3) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BWI
                                                    if (0xbdd4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xbe7c) {
                                    if (code < 0xbe28) {
                                        if (code < 0xbdf1) {
                                            if (code < 0xbdf0) {
                                                // Lo  [27] HANGUL SYLLABLE BWIG..HANGUL SYLLABLE BWIH
                                                if (0xbdd5 <= code && code <= 0xbdef) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE BYU
                                                if (0xbdf0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbe0c) {
                                                // Lo  [27] HANGUL SYLLABLE BYUG..HANGUL SYLLABLE BYUH
                                                if (0xbdf1 <= code && code <= 0xbe0b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbe0d) {
                                                    // Lo       HANGUL SYLLABLE BEU
                                                    if (0xbe0c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE BEUG..HANGUL SYLLABLE BEUH
                                                    if (0xbe0d <= code && code <= 0xbe27) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xbe45) {
                                            if (code < 0xbe29) {
                                                // Lo       HANGUL SYLLABLE BYI
                                                if (0xbe28 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbe44) {
                                                    // Lo  [27] HANGUL SYLLABLE BYIG..HANGUL SYLLABLE BYIH
                                                    if (0xbe29 <= code && code <= 0xbe43) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BI
                                                    if (0xbe44 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbe60) {
                                                // Lo  [27] HANGUL SYLLABLE BIG..HANGUL SYLLABLE BIH
                                                if (0xbe45 <= code && code <= 0xbe5f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbe61) {
                                                    // Lo       HANGUL SYLLABLE BBA
                                                    if (0xbe60 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE BBAG..HANGUL SYLLABLE BBAH
                                                    if (0xbe61 <= code && code <= 0xbe7b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xbeb5) {
                                        if (code < 0xbe98) {
                                            if (code < 0xbe7d) {
                                                // Lo       HANGUL SYLLABLE BBAE
                                                if (0xbe7c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE BBAEG..HANGUL SYLLABLE BBAEH
                                                if (0xbe7d <= code && code <= 0xbe97) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbe99) {
                                                // Lo       HANGUL SYLLABLE BBYA
                                                if (0xbe98 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbeb4) {
                                                    // Lo  [27] HANGUL SYLLABLE BBYAG..HANGUL SYLLABLE BBYAH
                                                    if (0xbe99 <= code && code <= 0xbeb3) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BBYAE
                                                    if (0xbeb4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xbeec) {
                                            if (code < 0xbed0) {
                                                // Lo  [27] HANGUL SYLLABLE BBYAEG..HANGUL SYLLABLE BBYAEH
                                                if (0xbeb5 <= code && code <= 0xbecf) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbed1) {
                                                    // Lo       HANGUL SYLLABLE BBEO
                                                    if (0xbed0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE BBEOG..HANGUL SYLLABLE BBEOH
                                                    if (0xbed1 <= code && code <= 0xbeeb) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbeed) {
                                                // Lo       HANGUL SYLLABLE BBE
                                                if (0xbeec === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbf08) {
                                                    // Lo  [27] HANGUL SYLLABLE BBEG..HANGUL SYLLABLE BBEH
                                                    if (0xbeed <= code && code <= 0xbf07) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BBYEO
                                                    if (0xbf08 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        else {
            if (code < 0xd1d8) {
                if (code < 0xc870) {
                    if (code < 0xc3bc) {
                        if (code < 0xc155) {
                            if (code < 0xc03c) {
                                if (code < 0xbf95) {
                                    if (code < 0xbf5c) {
                                        if (code < 0xbf25) {
                                            if (code < 0xbf24) {
                                                // Lo  [27] HANGUL SYLLABLE BBYEOG..HANGUL SYLLABLE BBYEOH
                                                if (0xbf09 <= code && code <= 0xbf23) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE BBYE
                                                if (0xbf24 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbf40) {
                                                // Lo  [27] HANGUL SYLLABLE BBYEG..HANGUL SYLLABLE BBYEH
                                                if (0xbf25 <= code && code <= 0xbf3f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbf41) {
                                                    // Lo       HANGUL SYLLABLE BBO
                                                    if (0xbf40 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE BBOG..HANGUL SYLLABLE BBOH
                                                    if (0xbf41 <= code && code <= 0xbf5b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xbf78) {
                                            if (code < 0xbf5d) {
                                                // Lo       HANGUL SYLLABLE BBWA
                                                if (0xbf5c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE BBWAG..HANGUL SYLLABLE BBWAH
                                                if (0xbf5d <= code && code <= 0xbf77) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbf79) {
                                                // Lo       HANGUL SYLLABLE BBWAE
                                                if (0xbf78 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xbf94) {
                                                    // Lo  [27] HANGUL SYLLABLE BBWAEG..HANGUL SYLLABLE BBWAEH
                                                    if (0xbf79 <= code && code <= 0xbf93) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BBOE
                                                    if (0xbf94 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xbfe8) {
                                        if (code < 0xbfb1) {
                                            if (code < 0xbfb0) {
                                                // Lo  [27] HANGUL SYLLABLE BBOEG..HANGUL SYLLABLE BBOEH
                                                if (0xbf95 <= code && code <= 0xbfaf) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE BBYO
                                                if (0xbfb0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xbfcc) {
                                                // Lo  [27] HANGUL SYLLABLE BBYOG..HANGUL SYLLABLE BBYOH
                                                if (0xbfb1 <= code && code <= 0xbfcb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xbfcd) {
                                                    // Lo       HANGUL SYLLABLE BBU
                                                    if (0xbfcc === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE BBUG..HANGUL SYLLABLE BBUH
                                                    if (0xbfcd <= code && code <= 0xbfe7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc005) {
                                            if (code < 0xbfe9) {
                                                // Lo       HANGUL SYLLABLE BBWEO
                                                if (0xbfe8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc004) {
                                                    // Lo  [27] HANGUL SYLLABLE BBWEOG..HANGUL SYLLABLE BBWEOH
                                                    if (0xbfe9 <= code && code <= 0xc003) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BBWE
                                                    if (0xc004 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc020) {
                                                // Lo  [27] HANGUL SYLLABLE BBWEG..HANGUL SYLLABLE BBWEH
                                                if (0xc005 <= code && code <= 0xc01f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc021) {
                                                    // Lo       HANGUL SYLLABLE BBWI
                                                    if (0xc020 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE BBWIG..HANGUL SYLLABLE BBWIH
                                                    if (0xc021 <= code && code <= 0xc03b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xc0c8) {
                                    if (code < 0xc075) {
                                        if (code < 0xc058) {
                                            if (code < 0xc03d) {
                                                // Lo       HANGUL SYLLABLE BBYU
                                                if (0xc03c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE BBYUG..HANGUL SYLLABLE BBYUH
                                                if (0xc03d <= code && code <= 0xc057) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc059) {
                                                // Lo       HANGUL SYLLABLE BBEU
                                                if (0xc058 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc074) {
                                                    // Lo  [27] HANGUL SYLLABLE BBEUG..HANGUL SYLLABLE BBEUH
                                                    if (0xc059 <= code && code <= 0xc073) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE BBYI
                                                    if (0xc074 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc091) {
                                            if (code < 0xc090) {
                                                // Lo  [27] HANGUL SYLLABLE BBYIG..HANGUL SYLLABLE BBYIH
                                                if (0xc075 <= code && code <= 0xc08f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE BBI
                                                if (0xc090 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc0ac) {
                                                // Lo  [27] HANGUL SYLLABLE BBIG..HANGUL SYLLABLE BBIH
                                                if (0xc091 <= code && code <= 0xc0ab) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc0ad) {
                                                    // Lo       HANGUL SYLLABLE SA
                                                    if (0xc0ac === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SAG..HANGUL SYLLABLE SAH
                                                    if (0xc0ad <= code && code <= 0xc0c7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xc101) {
                                        if (code < 0xc0e4) {
                                            if (code < 0xc0c9) {
                                                // Lo       HANGUL SYLLABLE SAE
                                                if (0xc0c8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE SAEG..HANGUL SYLLABLE SAEH
                                                if (0xc0c9 <= code && code <= 0xc0e3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc0e5) {
                                                // Lo       HANGUL SYLLABLE SYA
                                                if (0xc0e4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc100) {
                                                    // Lo  [27] HANGUL SYLLABLE SYAG..HANGUL SYLLABLE SYAH
                                                    if (0xc0e5 <= code && code <= 0xc0ff) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE SYAE
                                                    if (0xc100 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc138) {
                                            if (code < 0xc11c) {
                                                // Lo  [27] HANGUL SYLLABLE SYAEG..HANGUL SYLLABLE SYAEH
                                                if (0xc101 <= code && code <= 0xc11b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc11d) {
                                                    // Lo       HANGUL SYLLABLE SEO
                                                    if (0xc11c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SEOG..HANGUL SYLLABLE SEOH
                                                    if (0xc11d <= code && code <= 0xc137) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc139) {
                                                // Lo       HANGUL SYLLABLE SE
                                                if (0xc138 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc154) {
                                                    // Lo  [27] HANGUL SYLLABLE SEG..HANGUL SYLLABLE SEH
                                                    if (0xc139 <= code && code <= 0xc153) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE SYEO
                                                    if (0xc154 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            if (code < 0xc288) {
                                if (code < 0xc1e1) {
                                    if (code < 0xc1a8) {
                                        if (code < 0xc171) {
                                            if (code < 0xc170) {
                                                // Lo  [27] HANGUL SYLLABLE SYEOG..HANGUL SYLLABLE SYEOH
                                                if (0xc155 <= code && code <= 0xc16f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE SYE
                                                if (0xc170 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc18c) {
                                                // Lo  [27] HANGUL SYLLABLE SYEG..HANGUL SYLLABLE SYEH
                                                if (0xc171 <= code && code <= 0xc18b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc18d) {
                                                    // Lo       HANGUL SYLLABLE SO
                                                    if (0xc18c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SOG..HANGUL SYLLABLE SOH
                                                    if (0xc18d <= code && code <= 0xc1a7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc1c4) {
                                            if (code < 0xc1a9) {
                                                // Lo       HANGUL SYLLABLE SWA
                                                if (0xc1a8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE SWAG..HANGUL SYLLABLE SWAH
                                                if (0xc1a9 <= code && code <= 0xc1c3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc1c5) {
                                                // Lo       HANGUL SYLLABLE SWAE
                                                if (0xc1c4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc1e0) {
                                                    // Lo  [27] HANGUL SYLLABLE SWAEG..HANGUL SYLLABLE SWAEH
                                                    if (0xc1c5 <= code && code <= 0xc1df) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE SOE
                                                    if (0xc1e0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xc234) {
                                        if (code < 0xc1fd) {
                                            if (code < 0xc1fc) {
                                                // Lo  [27] HANGUL SYLLABLE SOEG..HANGUL SYLLABLE SOEH
                                                if (0xc1e1 <= code && code <= 0xc1fb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE SYO
                                                if (0xc1fc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc218) {
                                                // Lo  [27] HANGUL SYLLABLE SYOG..HANGUL SYLLABLE SYOH
                                                if (0xc1fd <= code && code <= 0xc217) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc219) {
                                                    // Lo       HANGUL SYLLABLE SU
                                                    if (0xc218 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SUG..HANGUL SYLLABLE SUH
                                                    if (0xc219 <= code && code <= 0xc233) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc251) {
                                            if (code < 0xc235) {
                                                // Lo       HANGUL SYLLABLE SWEO
                                                if (0xc234 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc250) {
                                                    // Lo  [27] HANGUL SYLLABLE SWEOG..HANGUL SYLLABLE SWEOH
                                                    if (0xc235 <= code && code <= 0xc24f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE SWE
                                                    if (0xc250 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc26c) {
                                                // Lo  [27] HANGUL SYLLABLE SWEG..HANGUL SYLLABLE SWEH
                                                if (0xc251 <= code && code <= 0xc26b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc26d) {
                                                    // Lo       HANGUL SYLLABLE SWI
                                                    if (0xc26c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SWIG..HANGUL SYLLABLE SWIH
                                                    if (0xc26d <= code && code <= 0xc287) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xc315) {
                                    if (code < 0xc2c1) {
                                        if (code < 0xc2a4) {
                                            if (code < 0xc289) {
                                                // Lo       HANGUL SYLLABLE SYU
                                                if (0xc288 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE SYUG..HANGUL SYLLABLE SYUH
                                                if (0xc289 <= code && code <= 0xc2a3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc2a5) {
                                                // Lo       HANGUL SYLLABLE SEU
                                                if (0xc2a4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc2c0) {
                                                    // Lo  [27] HANGUL SYLLABLE SEUG..HANGUL SYLLABLE SEUH
                                                    if (0xc2a5 <= code && code <= 0xc2bf) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE SYI
                                                    if (0xc2c0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc2f8) {
                                            if (code < 0xc2dc) {
                                                // Lo  [27] HANGUL SYLLABLE SYIG..HANGUL SYLLABLE SYIH
                                                if (0xc2c1 <= code && code <= 0xc2db) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc2dd) {
                                                    // Lo       HANGUL SYLLABLE SI
                                                    if (0xc2dc === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SIG..HANGUL SYLLABLE SIH
                                                    if (0xc2dd <= code && code <= 0xc2f7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc2f9) {
                                                // Lo       HANGUL SYLLABLE SSA
                                                if (0xc2f8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc314) {
                                                    // Lo  [27] HANGUL SYLLABLE SSAG..HANGUL SYLLABLE SSAH
                                                    if (0xc2f9 <= code && code <= 0xc313) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE SSAE
                                                    if (0xc314 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xc368) {
                                        if (code < 0xc331) {
                                            if (code < 0xc330) {
                                                // Lo  [27] HANGUL SYLLABLE SSAEG..HANGUL SYLLABLE SSAEH
                                                if (0xc315 <= code && code <= 0xc32f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE SSYA
                                                if (0xc330 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc34c) {
                                                // Lo  [27] HANGUL SYLLABLE SSYAG..HANGUL SYLLABLE SSYAH
                                                if (0xc331 <= code && code <= 0xc34b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc34d) {
                                                    // Lo       HANGUL SYLLABLE SSYAE
                                                    if (0xc34c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SSYAEG..HANGUL SYLLABLE SSYAEH
                                                    if (0xc34d <= code && code <= 0xc367) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc385) {
                                            if (code < 0xc369) {
                                                // Lo       HANGUL SYLLABLE SSEO
                                                if (0xc368 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc384) {
                                                    // Lo  [27] HANGUL SYLLABLE SSEOG..HANGUL SYLLABLE SSEOH
                                                    if (0xc369 <= code && code <= 0xc383) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE SSE
                                                    if (0xc384 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc3a0) {
                                                // Lo  [27] HANGUL SYLLABLE SSEG..HANGUL SYLLABLE SSEH
                                                if (0xc385 <= code && code <= 0xc39f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc3a1) {
                                                    // Lo       HANGUL SYLLABLE SSYEO
                                                    if (0xc3a0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SSYEOG..HANGUL SYLLABLE SSYEOH
                                                    if (0xc3a1 <= code && code <= 0xc3bb) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else {
                        if (code < 0xc609) {
                            if (code < 0xc4d5) {
                                if (code < 0xc448) {
                                    if (code < 0xc3f5) {
                                        if (code < 0xc3d8) {
                                            if (code < 0xc3bd) {
                                                // Lo       HANGUL SYLLABLE SSYE
                                                if (0xc3bc === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE SSYEG..HANGUL SYLLABLE SSYEH
                                                if (0xc3bd <= code && code <= 0xc3d7) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc3d9) {
                                                // Lo       HANGUL SYLLABLE SSO
                                                if (0xc3d8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc3f4) {
                                                    // Lo  [27] HANGUL SYLLABLE SSOG..HANGUL SYLLABLE SSOH
                                                    if (0xc3d9 <= code && code <= 0xc3f3) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE SSWA
                                                    if (0xc3f4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc411) {
                                            if (code < 0xc410) {
                                                // Lo  [27] HANGUL SYLLABLE SSWAG..HANGUL SYLLABLE SSWAH
                                                if (0xc3f5 <= code && code <= 0xc40f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE SSWAE
                                                if (0xc410 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc42c) {
                                                // Lo  [27] HANGUL SYLLABLE SSWAEG..HANGUL SYLLABLE SSWAEH
                                                if (0xc411 <= code && code <= 0xc42b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc42d) {
                                                    // Lo       HANGUL SYLLABLE SSOE
                                                    if (0xc42c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SSOEG..HANGUL SYLLABLE SSOEH
                                                    if (0xc42d <= code && code <= 0xc447) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xc481) {
                                        if (code < 0xc464) {
                                            if (code < 0xc449) {
                                                // Lo       HANGUL SYLLABLE SSYO
                                                if (0xc448 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE SSYOG..HANGUL SYLLABLE SSYOH
                                                if (0xc449 <= code && code <= 0xc463) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc465) {
                                                // Lo       HANGUL SYLLABLE SSU
                                                if (0xc464 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc480) {
                                                    // Lo  [27] HANGUL SYLLABLE SSUG..HANGUL SYLLABLE SSUH
                                                    if (0xc465 <= code && code <= 0xc47f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE SSWEO
                                                    if (0xc480 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc4b8) {
                                            if (code < 0xc49c) {
                                                // Lo  [27] HANGUL SYLLABLE SSWEOG..HANGUL SYLLABLE SSWEOH
                                                if (0xc481 <= code && code <= 0xc49b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc49d) {
                                                    // Lo       HANGUL SYLLABLE SSWE
                                                    if (0xc49c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SSWEG..HANGUL SYLLABLE SSWEH
                                                    if (0xc49d <= code && code <= 0xc4b7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc4b9) {
                                                // Lo       HANGUL SYLLABLE SSWI
                                                if (0xc4b8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc4d4) {
                                                    // Lo  [27] HANGUL SYLLABLE SSWIG..HANGUL SYLLABLE SSWIH
                                                    if (0xc4b9 <= code && code <= 0xc4d3) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE SSYU
                                                    if (0xc4d4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xc57c) {
                                    if (code < 0xc528) {
                                        if (code < 0xc4f1) {
                                            if (code < 0xc4f0) {
                                                // Lo  [27] HANGUL SYLLABLE SSYUG..HANGUL SYLLABLE SSYUH
                                                if (0xc4d5 <= code && code <= 0xc4ef) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE SSEU
                                                if (0xc4f0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc50c) {
                                                // Lo  [27] HANGUL SYLLABLE SSEUG..HANGUL SYLLABLE SSEUH
                                                if (0xc4f1 <= code && code <= 0xc50b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc50d) {
                                                    // Lo       HANGUL SYLLABLE SSYI
                                                    if (0xc50c === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE SSYIG..HANGUL SYLLABLE SSYIH
                                                    if (0xc50d <= code && code <= 0xc527) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc545) {
                                            if (code < 0xc529) {
                                                // Lo       HANGUL SYLLABLE SSI
                                                if (0xc528 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc544) {
                                                    // Lo  [27] HANGUL SYLLABLE SSIG..HANGUL SYLLABLE SSIH
                                                    if (0xc529 <= code && code <= 0xc543) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE A
                                                    if (0xc544 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc560) {
                                                // Lo  [27] HANGUL SYLLABLE AG..HANGUL SYLLABLE AH
                                                if (0xc545 <= code && code <= 0xc55f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc561) {
                                                    // Lo       HANGUL SYLLABLE AE
                                                    if (0xc560 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE AEG..HANGUL SYLLABLE AEH
                                                    if (0xc561 <= code && code <= 0xc57b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xc5b5) {
                                        if (code < 0xc598) {
                                            if (code < 0xc57d) {
                                                // Lo       HANGUL SYLLABLE YA
                                                if (0xc57c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE YAG..HANGUL SYLLABLE YAH
                                                if (0xc57d <= code && code <= 0xc597) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc599) {
                                                // Lo       HANGUL SYLLABLE YAE
                                                if (0xc598 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc5b4) {
                                                    // Lo  [27] HANGUL SYLLABLE YAEG..HANGUL SYLLABLE YAEH
                                                    if (0xc599 <= code && code <= 0xc5b3) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE EO
                                                    if (0xc5b4 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc5ec) {
                                            if (code < 0xc5d0) {
                                                // Lo  [27] HANGUL SYLLABLE EOG..HANGUL SYLLABLE EOH
                                                if (0xc5b5 <= code && code <= 0xc5cf) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc5d1) {
                                                    // Lo       HANGUL SYLLABLE E
                                                    if (0xc5d0 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE EG..HANGUL SYLLABLE EH
                                                    if (0xc5d1 <= code && code <= 0xc5eb) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc5ed) {
                                                // Lo       HANGUL SYLLABLE YEO
                                                if (0xc5ec === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc608) {
                                                    // Lo  [27] HANGUL SYLLABLE YEOG..HANGUL SYLLABLE YEOH
                                                    if (0xc5ed <= code && code <= 0xc607) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE YE
                                                    if (0xc608 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            if (code < 0xc73c) {
                                if (code < 0xc695) {
                                    if (code < 0xc65c) {
                                        if (code < 0xc625) {
                                            if (code < 0xc624) {
                                                // Lo  [27] HANGUL SYLLABLE YEG..HANGUL SYLLABLE YEH
                                                if (0xc609 <= code && code <= 0xc623) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE O
                                                if (0xc624 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc640) {
                                                // Lo  [27] HANGUL SYLLABLE OG..HANGUL SYLLABLE OH
                                                if (0xc625 <= code && code <= 0xc63f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc641) {
                                                    // Lo       HANGUL SYLLABLE WA
                                                    if (0xc640 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE WAG..HANGUL SYLLABLE WAH
                                                    if (0xc641 <= code && code <= 0xc65b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc678) {
                                            if (code < 0xc65d) {
                                                // Lo       HANGUL SYLLABLE WAE
                                                if (0xc65c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE WAEG..HANGUL SYLLABLE WAEH
                                                if (0xc65d <= code && code <= 0xc677) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc679) {
                                                // Lo       HANGUL SYLLABLE OE
                                                if (0xc678 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc694) {
                                                    // Lo  [27] HANGUL SYLLABLE OEG..HANGUL SYLLABLE OEH
                                                    if (0xc679 <= code && code <= 0xc693) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE YO
                                                    if (0xc694 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xc6e8) {
                                        if (code < 0xc6b1) {
                                            if (code < 0xc6b0) {
                                                // Lo  [27] HANGUL SYLLABLE YOG..HANGUL SYLLABLE YOH
                                                if (0xc695 <= code && code <= 0xc6af) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE U
                                                if (0xc6b0 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc6cc) {
                                                // Lo  [27] HANGUL SYLLABLE UG..HANGUL SYLLABLE UH
                                                if (0xc6b1 <= code && code <= 0xc6cb) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc6cd) {
                                                    // Lo       HANGUL SYLLABLE WEO
                                                    if (0xc6cc === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE WEOG..HANGUL SYLLABLE WEOH
                                                    if (0xc6cd <= code && code <= 0xc6e7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc705) {
                                            if (code < 0xc6e9) {
                                                // Lo       HANGUL SYLLABLE WE
                                                if (0xc6e8 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc704) {
                                                    // Lo  [27] HANGUL SYLLABLE WEG..HANGUL SYLLABLE WEH
                                                    if (0xc6e9 <= code && code <= 0xc703) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE WI
                                                    if (0xc704 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc720) {
                                                // Lo  [27] HANGUL SYLLABLE WIG..HANGUL SYLLABLE WIH
                                                if (0xc705 <= code && code <= 0xc71f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc721) {
                                                    // Lo       HANGUL SYLLABLE YU
                                                    if (0xc720 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE YUG..HANGUL SYLLABLE YUH
                                                    if (0xc721 <= code && code <= 0xc73b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else {
                                if (code < 0xc7c9) {
                                    if (code < 0xc775) {
                                        if (code < 0xc758) {
                                            if (code < 0xc73d) {
                                                // Lo       HANGUL SYLLABLE EU
                                                if (0xc73c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE EUG..HANGUL SYLLABLE EUH
                                                if (0xc73d <= code && code <= 0xc757) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc759) {
                                                // Lo       HANGUL SYLLABLE YI
                                                if (0xc758 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc774) {
                                                    // Lo  [27] HANGUL SYLLABLE YIG..HANGUL SYLLABLE YIH
                                                    if (0xc759 <= code && code <= 0xc773) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE I
                                                    if (0xc774 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc7ac) {
                                            if (code < 0xc790) {
                                                // Lo  [27] HANGUL SYLLABLE IG..HANGUL SYLLABLE IH
                                                if (0xc775 <= code && code <= 0xc78f) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc791) {
                                                    // Lo       HANGUL SYLLABLE JA
                                                    if (0xc790 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE JAG..HANGUL SYLLABLE JAH
                                                    if (0xc791 <= code && code <= 0xc7ab) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc7ad) {
                                                // Lo       HANGUL SYLLABLE JAE
                                                if (0xc7ac === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc7c8) {
                                                    // Lo  [27] HANGUL SYLLABLE JAEG..HANGUL SYLLABLE JAEH
                                                    if (0xc7ad <= code && code <= 0xc7c7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE JYA
                                                    if (0xc7c8 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                else {
                                    if (code < 0xc81c) {
                                        if (code < 0xc7e5) {
                                            if (code < 0xc7e4) {
                                                // Lo  [27] HANGUL SYLLABLE JYAG..HANGUL SYLLABLE JYAH
                                                if (0xc7c9 <= code && code <= 0xc7e3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE JYAE
                                                if (0xc7e4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc800) {
                                                // Lo  [27] HANGUL SYLLABLE JYAEG..HANGUL SYLLABLE JYAEH
                                                if (0xc7e5 <= code && code <= 0xc7ff) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc801) {
                                                    // Lo       HANGUL SYLLABLE JEO
                                                    if (0xc800 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE JEOG..HANGUL SYLLABLE JEOH
                                                    if (0xc801 <= code && code <= 0xc81b) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc839) {
                                            if (code < 0xc81d) {
                                                // Lo       HANGUL SYLLABLE JE
                                                if (0xc81c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc838) {
                                                    // Lo  [27] HANGUL SYLLABLE JEG..HANGUL SYLLABLE JEH
                                                    if (0xc81d <= code && code <= 0xc837) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE JYEO
                                                    if (0xc838 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc854) {
                                                // Lo  [27] HANGUL SYLLABLE JYEOG..HANGUL SYLLABLE JYEOH
                                                if (0xc839 <= code && code <= 0xc853) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc855) {
                                                    // Lo       HANGUL SYLLABLE JYE
                                                    if (0xc854 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                                else {
                                                    // Lo  [27] HANGUL SYLLABLE JYEG..HANGUL SYLLABLE JYEH
                                                    if (0xc855 <= code && code <= 0xc86f) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else {
                    if (code < 0xcd24) {
                        if (code < 0xcabd) {
                            if (code < 0xc989) {
                                if (code < 0xc8fc) {
                                    if (code < 0xc8a9) {
                                        if (code < 0xc88c) {
                                            if (code < 0xc871) {
                                                // Lo       HANGUL SYLLABLE JO
                                                if (0xc870 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                // Lo  [27] HANGUL SYLLABLE JOG..HANGUL SYLLABLE JOH
                                                if (0xc871 <= code && code <= 0xc88b) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc88d) {
                                                // Lo       HANGUL SYLLABLE JWA
                                                if (0xc88c === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                            else {
                                                if (code < 0xc8a8) {
                                                    // Lo  [27] HANGUL SYLLABLE JWAG..HANGUL SYLLABLE JWAH
                                                    if (0xc88d <= code && code <= 0xc8a7) {
                                                        return boundaries_1.CLUSTER_BREAK.LVT;
                                                    }
                                                }
                                                else {
                                                    // Lo       HANGUL SYLLABLE JWAE
                                                    if (0xc8a8 === code) {
                                                        return boundaries_1.CLUSTER_BREAK.LV;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    else {
                                        if (code < 0xc8c5) {
                                            if (code < 0xc8c4) {
                                                // Lo  [27] HANGUL SYLLABLE JWAEG..HANGUL SYLLABLE JWAEH
                                                if (0xc8a9 <= code && code <= 0xc8c3) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                // Lo       HANGUL SYLLABLE JOE
                                                if (0xc8c4 === code) {
                                                    return boundaries_1.CLUSTER_BREAK.LV;
                                                }
                                            }
                                        }
                                        else {
                                            if (code < 0xc8e0) {
                                                // Lo  [27] HANGUL SYLLABLE JOEG..HANGUL SYLLABLE JOEH
                                                if (0xc8c5 <= code && code <= 0xc8df) {
                                                    return boundaries_1.CLUSTER_BREAK.LVT;
                                                }
                                            }
                                            else {
                                                if (code < 0xc8e1) {
                                                    // Lo       HANGUL SYLLABLE JY